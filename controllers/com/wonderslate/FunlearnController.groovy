package com.wonderslate

import com.ccavenue.security.AesCryptUtil
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.wonderslate.WsLibrary.WsLibraryService
import com.wonderslate.admin.AdminService
import com.wonderslate.cache.DataProviderService
import com.wonderslate.cache.SearchService
import com.wonderslate.data.*
import com.wonderslate.institute.*
import com.wonderslate.log.*
import com.wonderslate.logs.AsyncLogsService
import com.wonderslate.prepjoy.DailyTestsMst
import com.wonderslate.publish.*
import com.wonderslate.shop.BookPriceDtl
import com.wonderslate.usermanagement.*
import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import groovy.sql.Sql
import org.apache.commons.io.FileUtils
import org.grails.web.json.JSONArray
import org.imgscalr.Scalr
import org.springframework.web.multipart.MultipartFile
import org.springframework.web.multipart.MultipartHttpServletRequest
import org.w3c.tidy.Tidy
import org.xhtmlrenderer.pdf.ITextRenderer
import pl.touk.excel.export.WebXlsxExporter

import javax.imageio.IIOImage
import javax.imageio.ImageIO
import javax.imageio.ImageWriteParam
import javax.imageio.ImageWriter
import javax.imageio.stream.ImageOutputStream
import javax.servlet.http.Cookie
import javax.servlet.http.HttpServletResponse
import java.awt.image.BufferedImage
import java.nio.charset.StandardCharsets
import java.nio.file.Files
import java.nio.file.Paths
import java.text.DateFormat
import java.text.SimpleDateFormat
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream
import java.nio.file.*;

class FunlearnController {
    SpringSecurityService springSecurityService
    UserManagementService userManagementService
    UtilService utilService
    def redisService
    DataProviderService dataProviderService
    DataNotificationService dataNotificationService
    AsyncLogsService asyncLogsService
    MetainfoService metainfoService
    SearchService searchService
    WsLibraryService wsLibraryService
    SecurityService securityService
    AdminService adminService
    PrepjoyService prepjoyService
    ContentDeliveryService  contentDeliveryService


    /**
     * API to validate token for CloudFront signed URLs
     * Takes reference from validateAndConsumeToken function in lambdaFn.js
     */
    @Transactional
    def validateToken() {
        try {
            String token = params.token

            if (!token) {
                render([status: "error", message: "Token is required"] as JSON)
                return
            }

            // Check if token exists and is not used (keyValue = "false")
            KeyValueMst tokenRecord = KeyValueMst.findByKeyName(token)

            if (!tokenRecord) {
                render([status: "error", message: "Token not found"] as JSON)
                return
            }

            // Check if token is already used (keyValue = "true")
            if (tokenRecord.keyValue == "true") {
                render([status: "error", message: "Token already used"] as JSON)
                return
            }

            // Mark token as used by updating keyValue to "true"
            tokenRecord.keyValue = "true"
            tokenRecord.save(failOnError: true, flush: true)

            render([status: "success", message: "Token validated and consumed successfully"] as JSON)

        } catch (Exception e) {
            log.error("Error validating token: ${e.message}", e)
            render([status: "error", message: "Internal server error"] as JSON)
        }
    }

    def index() {
        session.setAttribute("syllabusType", "school");
        session.setAttribute("country", "India");
        if (springSecurityService.currentUser != null) redirect([uri: '/funlearn/home'])
    }

    def scoreDetails() {}

    def signIn() {}
    def signupNew(){}

    def signUp() {}

    def forgotpassword() {}

    def chaptersModal() {}

    def collectSingleChapterDetails(){
        ChaptersMst chaptersMst = dataProviderService.getChaptersMst(new Long(params.chapterId))
        BooksTagDtl booksTagDtl = dataProviderService.getBooksTagDtl(chaptersMst.bookId)
        def batchPresent=false

        String userBatches="";
        if(springSecurityService.currentUser!=null){
            if(redisService.("bookInBatch_"+chaptersMst.bookId)==null) dataProviderService.bookInBatch(chaptersMst.bookId)
            if("true".equals(redisService.("bookInBatch_"+chaptersMst.bookId))) {
                if (redisService.("userStudentBatchString_" + springSecurityService.currentUser.username) == null) {
                    dataProviderService.getUserBatchesAsStudent(springSecurityService.currentUser.username);
                }

                userBatches = redisService.("userStudentBatchString_" + springSecurityService.currentUser.username)
                if(userBatches.length()>0){
                    batchPresent=true
                }


            }
        }

        def json = ['jsonChapterDetails': collectSingleOptimisedChapterDetailsImpl(chaptersMst,booksTagDtl,batchPresent,userBatches)]
        render json as JSON


    }

    @Transactional
    def collectSingleOptimisedChapterDetailsImpl(chaptersMst,booksTagDtl,batchPresent,userBatches){

        def jsonChapterDetails = []

        String seenResources=""

        def defaultResources,addedResources=null,batchResources=null
        if(redisService.("defaultChapterDetail_"+chaptersMst.id)==null) dataProviderService.defaultChapterResourceDtls(chaptersMst.id,booksTagDtl,chaptersMst.name,chaptersMst.chapterDesc,""+chaptersMst.bookId)
        defaultResources = redisService.("defaultChapterDetail_"+chaptersMst.id)
        if(batchPresent){
            List<String> batchIds = Arrays.asList(userBatches.split("\\s*,\\s*"));
            List batchResourcesList = BatchResourcesDtl.findAllByChapterIdAndBatchIdInList(chaptersMst.id,batchIds)
            if(batchResourcesList.size()>0) {
                List resourceIds = []
                for (int k = 0; k < batchResourcesList.size(); k++) {
                    resourceIds << batchResourcesList[k].resId
                }
                List resources = ResourceDtl.findAllByIdInList(resourceIds)
                batchResources = dataProviderService.getResourceDetails(resources, booksTagDtl, chaptersMst.name, chaptersMst.chapterDesc, chaptersMst.bookId)
            }
        }
        if(springSecurityService.currentUser!=null) {
            List userResources = ResourceDtl.findAllByChapterIdAndCreatedBy(chaptersMst.id, springSecurityService.currentUser.username)
            addedResources = dataProviderService.getResourceDetails(userResources,booksTagDtl,chaptersMst.name,chaptersMst.chapterDesc,chaptersMst.bookId)
            seenResources = redisService.("seenResources_"+springSecurityService.currentUser.username+"_"+chaptersMst.id)
        }
        def json =
                [
                        'defaultResources': defaultResources,
                        'addedResources': addedResources,
                        'batchResources': batchResources,
                        'status' : defaultResources ? "OK" : "Nothing present",
                        'mode'   : params.mode,
                        seenResouces: seenResources,
                        chapterId: chaptersMst.id
                ]

        return json


    }
    @Transactional
    def collectChapterDetails(){
        if (redisService.("chapters_" + params.bookId) == null) {
            dataProviderService.getChaptersList(new Long(params.bookId));
        }
        List chaptersList = new JsonSlurper().parseText(redisService.("chapters_" + params.bookId))
        def jsonChapterDetails = []
        def batchPresent=false
        BooksTagDtl booksTagDtl = dataProviderService.getBooksTagDtl(new Long(params.bookId))
        String userBatches="";
        if(springSecurityService.currentUser!=null){
            if(redisService.("bookInBatch_"+params.bookId)==null) dataProviderService.bookInBatch(new Long(params.bookId))
            if("true".equals(redisService.("bookInBatch_"+params.bookId))) {
                if (redisService.("userStudentBatchString_" + springSecurityService.currentUser.username) == null) {
                    dataProviderService.getUserBatchesAsStudent(springSecurityService.currentUser.username);
                }

                userBatches = redisService.("userStudentBatchString_" + springSecurityService.currentUser.username)
                if(userBatches.length()>0){
                    batchPresent=true
                }
            }
        }
        String seenResources=""
        ChaptersMst chaptersMst
        for(int i=0;i<chaptersList.size();i++) {
            chaptersMst = dataProviderService.getChaptersMst(new Long(chaptersList[i].id))

            jsonChapterDetails << collectSingleOptimisedChapterDetailsImpl(chaptersMst,booksTagDtl,batchPresent,userBatches)

        }
        def json = ['jsonChapterDetails':jsonChapterDetails,
                    'chaptersList':chaptersList]
        render json as JSON


    }

    @Transactional
    def getChapterDetails(Long chapterId,String authorMode,String fromWeb){
         String seenResources ="";
        ChaptersMst chaptersMst = dataProviderService.getChaptersMst(chapterId)
        String siteName = grailsApplication.config.grails.appServer.siteName
        def expiryDate = null
        ExamMst examMst = null
        def examDtl = null
        boolean hasQuiz = false
        boolean previewChapter=false

        if(redisService.("defaultResourceIDs_"+chapterId)==null){
            dataProviderService.getChapterDefaultResourcesAsString(chapterId);
        }
        String[] resourseIDs = redisService.("defaultResourceIDs_"+chapterId).split(",")
        List resources=[];

        for(int i=0;i<resourseIDs.length;i++) {
            if(!"".equals(resourseIDs[i]))
                resources.add(dataProviderService.getResourceDtl(new Long(resourseIDs[i])))
        }

        BooksTagDtl booksTagDtl = dataProviderService.getBooksTagDtl(chaptersMst.bookId)
        BooksMst booksMst = dataProviderService.getBooksMst(chaptersMst.bookId)
        Boolean revisionPresent = true
        Boolean boughtTestSeries = false
        if("true".equals(chaptersMst.previewChapter)) {
              previewChapter = true
        }

        if(springSecurityService.currentUser!=null) {
            BooksPermission booksPermission = BooksPermission.findByBookIdAndUsername(chaptersMst.bookId,springSecurityService.currentUser.username)
            if(booksPermission!=null&&!"ADDEDFROMINSTITUTE".equals(booksPermission.poType)) {
                expiryDate = booksPermission.expiryDate
                if("true".equals(booksPermission.testsPurchased)) boughtTestSeries=true
            }
            //update last access
            userManagementService.updateChapterAccess(chaptersMst.bookId,chapterId)

            if(redisService.("bookInBatch_"+chaptersMst.bookId)==null) dataProviderService.bookInBatch(chaptersMst.bookId)
            if("true".equals(redisService.("bookInBatch_"+chaptersMst.bookId))) {

                //batch related resources
                // if(session["userBatches"]==null)
                session["userBatches"] = userManagementService.userBatches()
                //first check if user is in batch
                if (session["userBatches"].length() > 0) {

                    if (redisService.("userStudentBatch_" + springSecurityService.currentUser.username) == null) {
                        dataProviderService.getUserBatchesAsStudent(springSecurityService.currentUser.username);
                    }

                    List userBatchesList = new JsonSlurper().parseText(redisService.("userStudentBatch_" + springSecurityService.currentUser.username));

                    def tempUserBatches = []
                    userBatchesList.each { userBatch ->
                        tempUserBatches << userBatch.batchId
                        BooksBatchDtl booksBatchDtl = BooksBatchDtl.findByBookIdAndBatchId(chaptersMst.bookId, userBatch.batchId)
                        if (!userManagementService.isInstructor()) {
                            //non cached.. have to cache it some point of time. Since it is only for instructors.. ok for the timebeing

                            if (booksBatchDtl != null &&"true".equals(booksBatchDtl.instructorControlled)&& !"true".equals(chaptersMst.previewChapter)) {
                                resources = [];
                            }
                        }
                        if(booksBatchDtl!=null) {
                            //if part of library, show the test series
                            boughtTestSeries = true

                        }
                    }
                    tempUserBatches.each { tempUserBatch ->
                        if (redisService.("getBatchRes_" + chapterId + "_" + tempUserBatch) == null) {
                            dataProviderService.getBatchResourcesForChapter(chapterId, new Long(tempUserBatch));
                        }
                        if (redisService.("getBatchRes_" + chapterId + "_" + tempUserBatch) != null) {
                            List batchResources = new JsonSlurper().parseText(redisService.("getBatchRes_" + chapterId + "_" + tempUserBatch));
                            batchResources.each { batchResource ->
                                resources.add(dataProviderService.getResourceDtl(new Long(batchResource.id)))
                            }
                        }
                    }
                }
            }
            if(redisService.("userResources_"+springSecurityService.currentUser.username+"_"+chapterId)==null){
                dataProviderService.getChapterResourcesForUser(chapterId,springSecurityService.currentUser.username);
            }
            //user created resources
            List userCreatedResources = new JsonSlurper().parseText(redisService.("userResources_"+springSecurityService.currentUser.username+"_"+chapterId));
            userCreatedResources.each{userCreatedResource ->
                resources.add(dataProviderService.getResourceDtl(new Long(userCreatedResource.id)))

            }
            if(redisService.("seenResources_"+springSecurityService.currentUser.username+"_"+chapterId)==null){
                dataProviderService.updateUsageList(chapterId)
            }
            seenResources = redisService.("seenResources_"+springSecurityService.currentUser.username+"_"+chapterId)

            User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
            if (("" + user.publisherId).equals("" + booksMst.publisherId) || (user.authorities.any {
                it.authority == "ROLE_WS_CONTENT_CREATOR"
            } && user.publisherId == null)||(user.authorities.any {
                it.authority == "ROLE_WS_SALES_TEAM"
            } && user.publisherId == null)) {
                boughtTestSeries = true

            }

        }

        //remove the duplicates
        if(resources.size()>0) resources = resources.unique()
        boolean skipVideoLink;
        //this step is not required. But to not to make any changes in the frontend, doing this.
        String creatorName = ""
        String createdBy=""
        String securityKeys = ""
        String zoomLevel = ""
        int siteId = getSiteId(request).intValue()
        if(siteId==1||siteId==25) {
            if(redisService.("securityKeys_"+siteId)==null) dataProviderService.getSecurityKeys(siteId)
            securityKeys = redisService.("securityKeys_"+siteId)
        }

        boolean publisherBlocksVideo = false;
        if(booksMst.publisherId!=null) {
            Publishers publishers = dataProviderService.getPublisher(booksMst.publisherId)
            if (publishers != null && ("true").equals(publishers.showVideoOnlyInApp)) publisherBlocksVideo = true;
        }
        boolean skipMCQs = true
        Boolean testSeriesBook = false

        List bookPriceDtls = BookPriceDtl.findAllByBookId(booksMst.id)
        bookPriceDtls.each{ bookPrice->
        if("testSeries".equals(bookPrice.bookType)){
                testSeriesBook = true
            }
        }
        
        List jsonChapter = resources.collect { comp ->
            skipVideoLink=false;
            skipMCQs = false;

            if("Multiple Choice Questions".equals(comp.resType)) hasQuiz = true
            if(booksMst.publisherId!=null){
                if(publisherBlocksVideo&&"Reference Videos".equals(comp.resType)) {
                    skipVideoLink=true
                }
                if("Multiple Choice Questions".equals(comp.resType)&&!boughtTestSeries&&!previewChapter&&testSeriesBook) {
                    skipMCQs = true
                }
            }
            if("bookauthor".equals(authorMode)) {
                skipVideoLink=false
                skipMCQs = false
                User user = dataProviderService.getUserMst(comp.createdBy)
                if(user!=null){
                    creatorName = user.name
                }else {
                    creatorName = comp.createdBy
                }

                boughtTestSeries = true

            };
            createdBy = comp.createdBy;

            String fileType = "";

            if (comp.resLink != null && ("" + comp.resLink).lastIndexOf('.') != -1) {
                fileType = ("" + comp.resLink).substring(("" + comp.resLink).lastIndexOf('.') + 1)
            };
            boolean ebook=false
            if(comp.filename!=null&&(comp.filename.indexOf(".pdf")!=-1||comp.filename.indexOf(".zip")!=-1)) ebook=true
            def testStartDate="",testEndDate="",testStarted="",testEnded="",testResultDate="",testResultAnnounced=""
            if(comp.testStartDate!=null) {
                testStartDate = utilService.convertDate(comp.testStartDate,"UTC","IST")
                if(Calendar.getInstance().getTime().compareTo(comp.testStartDate)>0) {
                    testStarted="true"
                    if(comp.testResultDate==null) testResultAnnounced="true"
                }
            }

            if(comp.testEndDate!=null) {
                testEndDate = utilService.convertDate(comp.testEndDate,"UTC","IST")
                if(Calendar.getInstance().getTime().compareTo(comp.testEndDate)>0) {
                    testEnded="true"
                    if(comp.testResultDate==null) testResultAnnounced="true"
                }

            }
            if(comp.testResultDate!=null) {
                testResultDate = utilService.convertDate(comp.testResultDate,"UTC","IST")
                if(Calendar.getInstance().getTime().compareTo(comp.testResultDate)>0){
                    testResultAnnounced="true"
                }
            }

            if (comp.examId != null) {
                examMst = dataProviderService.getExamMst(comp.examId)
                if (examMst != null) {
                    if(redisService.("examDtl_" +examMst.id)==null) dataProviderService.getExamDtls(examMst.id)
                    examDtl = redisService.("examDtl_" +examMst.id)
                }
            }


            return [id                 : skipMCQs?"":comp.id, topicId: comp.chapterId, resType: comp.resType, resLink: skipVideoLink||skipMCQs?"":comp.resLink != null && !"blank".equals(comp.resLink) ?comp.resLink.replaceAll(':','#'):"", resName: comp.resourceName, dateCreated: comp.dateCreated,
                    topicName          : chaptersMst.name, syllabus: (booksTagDtl!=null)?booksTagDtl.syllabus: 'syllabus', grade: (booksTagDtl!=null)?booksTagDtl.grade:'grade',
                    secureVideoLink : securityKeys,
                    subject:(booksTagDtl!=null)?booksTagDtl.subject: 'subject', creatorname: creatorName,
                    sharing: comp.sharing,
                    downloadlink1:  comp.downloadlink1!=null?comp.downloadlink1.replaceAll(':','#'):"",
                    downloadlink2:   comp.downloadlink2!=null?comp.downloadlink2.replaceAll(':','#'):"",
                    downloadlink3:  comp.downloadlink3!=null?comp.downloadlink3.replaceAll(':','#'):"",
                    quizMode           : comp.quizMode,
                    language1   : (comp.language1 != null) ? comp.language1 : "",
                    language2   : (comp.language2 != null) ? comp.language2 : "",
                    examDtl    : examDtl,
                    totalMarks : examMst?examMst.totalMarks:"",
                    totalQuestion : examMst?examMst.noOfQuestions:"",
                    totalTime :examMst?examMst.totalTime:"",
                    allowReAttempt:comp.allowReAttempt!=null?comp.allowReAttempt:"",
                    chapterDesc        : chaptersMst.chapterDesc,  bookId: chaptersMst.bookId, fileType: fileType, fileSize: comp.fileSize,revisionPresent:revisionPresent,
                    level: (booksTagDtl!=null)?booksTagDtl.level: 'level',
                    filename:comp.filename,
                    testStartDate: testStartDate, testEndDate: testEndDate,
                    testStarted:testStarted,testEnded:testEnded,
                    videoPlayer:comp.videoPlayer==null?"youtube":comp.videoPlayer,
                    allowComments:comp.allowComments, displayComments:comp.displayComments,
                    testResultDate: testResultDate, testResultAnnounced: testResultAnnounced,
                    createdBy: createdBy,
                    canEdit  : (springSecurityService.currentUser != null && springSecurityService.currentUser.username.equals(comp.createdBy)) ? "true" : "false",zoomLevel: comp.zoomLevel !=null ?comp.zoomLevel:'']


        }
        if(redisService.("validityExtensionDtls_"+chaptersMst.bookId)==null) dataProviderService.getValidityExtensionDtls((Integer)chaptersMst.bookId )
        if(redisService.("suggestedVideos_"+chaptersMst.id)==null) dataProviderService.getRelatedVideosFromDB((""+chaptersMst.id))

        def json =
                [
                        'results': jsonChapter,
                        'status' : jsonChapter ? "OK" : "Nothing present",
                        'mode'   : authorMode,
                        seenResouces: seenResources,
                        chapterId: chapterId,
                        expiryDate: expiryDate,
                        extensionPrices : redisService.("validityExtensionDtls_"+chaptersMst.bookId),
                        hasQuiz : hasQuiz,
                        suggestedVideos: redisService.("suggestedVideos_"+chaptersMst.id),
                        testSeriesBook: testSeriesBook,
                        boughtTestSeries:boughtTestSeries,
                        previewChapter:previewChapter

                ]
        return json
    }

    def chapterDetails(){

        boolean fullBook=false;
        if("true".equals(params.fullBook)) fullBook=true
        if(fullBook){
            def jsonChapterDetails = []
            if (redisService.("chapters_" + params.bookId) == null) {
                dataProviderService.getChaptersList(new Long(params.bookId));
            }
            List chaptersList = new JsonSlurper().parseText(redisService.("chapters_" + params.bookId))

            for(int i=0;i<chaptersList.size();i++) {
                jsonChapterDetails << getChapterDetails(new Long(chaptersList[i].id), params.mode, params.fromWeb)

            }
            def json = ['jsonChapterDetails':jsonChapterDetails,
                        'chaptersList':chaptersList]
            render json as JSON
        }else{
            Long chapterId = new Long(params.topicId)
            def json = getChapterDetails(chapterId,params.mode,params.fromWeb)
            render json as JSON
        }
    }

    def latestChapterDetails() {
        String syllabusType = params.syllabusType != null && !"".equals(params.syllabusType) ? params.syllabusType : "school";
        String country = params.country != null && !"".equals(params.country) ? params.country : "India";

        String sql = "select rd.id, rd.topicId, rd.resType, rd.resLink, rd.resourceName, rd.dateCreated, " +
                "tm.topicName, tm.syllabus, tm.grade, tm.subject from ResourceDtl rd, TopicMst tm " +
                "where tm.id=rd.topicId and rd.resType='Mind Maps' and rd.sharing='public' " +
                "and tm.country='" + country + "' and tm.syllabusType='" + syllabusType + "' " +
                "order by rd.resType, rd.dateCreated desc";
        List chapterDetails1 = ResourceDtl.executeQuery(sql, [max: 3]);

        sql = "select rd.id, rd.topicId, rd.resType, rd.resLink, rd.resourceName, rd.dateCreated, " +
                "tm.topicName, tm.syllabus, tm.grade, tm.subject from ResourceDtl rd, TopicMst tm " +
                "where tm.id=rd.topicId and rd.resType='Videos' and rd.sharing='public' " +
                "and tm.country='" + country + "' and tm.syllabusType='" + syllabusType + "' " +
                "order by rd.resType, rd.dateCreated desc";
        List chapterDetails2 = ResourceDtl.executeQuery(sql, [max: 3]);

        sql = "select rd.id, rd.topicId, rd.resType, rd.resLink, rd.resourceName, rd.dateCreated, " +
                "tm.topicName, tm.syllabus, tm.grade, tm.subject from ResourceDtl rd, TopicMst tm, ResourceType rt " +
                "where tm.id=rd.topicId and  rd.resType=rt.resourceType and rt.useType='quiz' and rd.sharing='public' " +
                "and tm.country='" + country + "' and tm.syllabusType='" + syllabusType + "' " +
                "order by rd.resType, rd.dateCreated desc";
        List chapterDetails3 = ResourceDtl.executeQuery(sql, [max: 3]);

        List chapterDetails = chapterDetails1 + chapterDetails2 + chapterDetails3;

        List jsonChapter = chapterDetails.collect { comp ->
            return [id: comp[0], topicId: comp[1], resType: comp[2], resLink: comp[3], resName: comp[4], dateCreated: comp[5], topicName: comp[6], syllabus: comp[7], grade: comp[8], subject: comp[9]]
        }

        def json =
                [
                        'results': jsonChapter,
                        'status' : jsonChapter ? "OK" : "Nothing present"
                ]
        render json as JSON
    }

    def latestResTypeDetails() {
        String syllabusType = params.syllabusType != null && !"".equals(params.syllabusType) ? params.syllabusType : "school";
        String country = params.country != null && !"".equals(params.country) ? params.country : "India";

        String strTypes = "'" + params.resType + "'";
        if (params.resType == "Multiple Choice Questions" || params.resType == "Fill in the blanks" || params.resType == "True or False" || params.resType == "Opposites") {
            strTypes = "'Multiple Choice Questions','Fill in the blanks','True or False','Opposites'";
        }

        String sql = "select rd.id, rd.topicId, rd.resType, rd.resLink, rd.resourceName, rd.dateCreated, tm.topicName," +
                " tm.syllabus, tm.grade, tm.subject from ResourceDtl rd, TopicMst tm where tm.id=rd.topicId and" +
                " rd.resType in (" + strTypes + ") and tm.country='" + country + "' and tm.syllabusType='" + syllabusType + "'" +
                " and tm.grade=" + params.grade + " and tm.syllabus='" + params.board + "'" +
                " and rd.sharing='public' order by rd.dateCreated desc";
        List resTypeDetails = ResourceDtl.executeQuery(sql, [max: 99]);

        List jsonChapter = resTypeDetails.collect { comp ->
            return [id: comp[0], topicId: comp[1], resType: comp[2], resLink: comp[3], resName: comp[4], dateCreated: comp[5], topicName: comp[6], syllabus: comp[7], grade: comp[8], subject: comp[9]]
        }

        Cookie cookie = new Cookie("syllabus", params.board);
        cookie.maxAge = 99999999;
        response.addCookie(cookie);
        cookie = new Cookie("grade", "" + params.grade);
        cookie.maxAge = 99999999;
        response.addCookie(cookie);
        cookie = new Cookie("subject", "");
        cookie.maxAge = 99999999;
        response.addCookie(cookie);

        def json =
                [
                        'results': jsonChapter,
                        'status' : jsonChapter ? "OK" : "Nothing present"
                ]
        render json as JSON
    }


    @Transactional
    def quizQuestions() {
        ResourceDtl resourceDtl;
        def sql
        List questions
        ExamMst examMst = null
        List examDtl = null

        boolean canAccess = true;
        String isPassage = "false";
        String passage = "";
        String description;
        String canReorderQuestions;
        List chaptersList = null;
        TestsMst testsMst = null
        String testSeries = "false";
        String language1=""
        String language2=""
        String sort=""
        def testEndDate = null
        def testEndDateWeb =null
        HashMap chaptersMap =  new HashMap()
        if ("true".equals(params.createtest)) {
            def chapters = params.chaptersList.split(',').collect { it as int }
            if(!"sage".equals(session["entryController"])) {
                chapters.each { chapter ->
                    if (!userManagementService.canSeeChapter(new Long(chapter))) canAccess = false;

                }
            }
            if (canAccess) {
                sql = "select res_link,chapter_id,cm.name chapterName,bm.title from resource_dtl rd,chapters_mst cm,books_mst bm  where res_type='" + params.resType + "' and chapter_id in (" + params.chaptersList + ")  and cm.id=rd.chapter_id\n" +
                        "  and bm.id=cm.book_id";
                def dataSource = grailsApplication.mainContext.getBean('dataSource')
                def sql1 = new Sql(dataSource)
                def quizIdsList = sql1.rows(sql);
                def quizIds = [];
                def objIds = [];
                String stringQuizIds="";
                quizIdsList.each { quiz ->
                    quizIds << new Integer("" + quiz[0])
                    stringQuizIds +=quiz[0]+","
                    chaptersMap.put(""+quiz[0],""+quiz[1])
                }
                stringQuizIds = stringQuizIds.substring(0,stringQuizIds.length()-1)
                sql = "SELECT t1.id FROM objective_mst AS t1 JOIN (SELECT id FROM objective_mst  where quiz_id in" +
                        " ("+stringQuizIds+") ORDER BY RAND() LIMIT "+params.noOfQuestions+") as t2 ON t1.id=t2.id"
                dataSource = grailsApplication.mainContext.getBean('dataSource')
                sql1 = new Sql(dataSource)
                def quizList = sql1.rows(sql);
                quizList.each { quiz ->
                    objIds << new Integer("" + quiz[0])
                }
                questions = ObjectiveMst.findAllByIdInList(objIds)


                chaptersList = quizIdsList.collect { comp ->
                    return [quizId: comp[0], id: comp[1], name: comp[2], subject: comp[3]]
                }



            }
        } else {
            if (params.inputDate != null) {
                if (redisService.("currentAffairsQuiz_" + params.inputDate) == null) adminService.getCurrentAffairsQuizId(params.inputDate)
                if (!"-1".equals(redisService.("currentAffairsQuiz_" + params.inputDate)))
                    resourceDtl = ResourceDtl.findById(new Integer(redisService.("currentAffairsQuiz_" + params.inputDate)))
            } else if (params.resId != null) {
                resourceDtl = ResourceDtl.findById(new Long(params.resId))
            } else {
                List resourceDtls = ResourceDtl.findAllByResLink(params.quizId)
                canAccess = false;

                resourceDtls.each { resDtl ->
                    if (canSeeResource(resDtl)) {
                        canAccess = true
                        resourceDtl = resDtl
                    } else {
                    }
                }
                if (!canAccess) {
                    resourceDtl = resourceDtls[0]
                }
            }
            if (resourceDtl != null) {
                if (resourceDtl.examId != null) {
                    examMst = ExamMst.findById(resourceDtl.examId)
                    if (examMst != null) {
                        examDtl = ExamDtl.findAllByExamId(examMst.id)

                    }

                }

                if (resourceDtl.examSyllabus != null && !"".equals(resourceDtl.examSyllabus)) chaptersList = ExamChapterMst.findAllByExam(resourceDtl.examSyllabus)
                canAccess = canSeeResource(resourceDtl);
                description = resourceDtl.description
                canReorderQuestions = resourceDtl.canReorderQuestions

                if (canAccess) {
                    chaptersMap.put(""+resourceDtl.resLink,""+resourceDtl.chapterId)
                    ObjectiveMst objectiveMst = ObjectiveMst.findByQuizIdAndQuizSort(new Integer(resourceDtl.resLink),0)
                    if(objectiveMst != null) sort = "quizSort"
                    else sort = "id"
                    questions = ObjectiveMst.findAllByQuizId(new Integer(resourceDtl.resLink),[sort: sort])
                    // logic to remove expired questions


                    if ("passage".equals(resourceDtl.quizMode)) {
                        isPassage = "true";
                        passage = resourceDtl.chapterDesc;
                    }

                    if (resourceDtl.testStartDate != null && resourceDtl.testEndDate != null) {
                        testSeries = "true"
                        testEndDate = utilService.convertDate(resourceDtl.testEndDate, "UTC", "IST")
                        testEndDateWeb = resourceDtl.testEndDate
                    }
                }
            }
        }

        if (canAccess) {

            long seed = System.nanoTime();
            if ("true".equals(canReorderQuestions)) Collections.shuffle(questions, new Random(seed));
            String directions, section

            if ("true".equals(params.createtest)) {
                if(!"71".equals(""+session["siteId"])){
                    Collections.shuffle(questions, new Random(seed));
                }
                int extraQuestions = questions.size() - Integer.parseInt(params.noOfQuestions);

                for (int i = 0; i < extraQuestions; i++) questions.remove((questions.size() - 1));

                questions.each { quiz ->
                    if (quiz.directionId != null) {
                        DirectionsMst directionsMst = DirectionsMst.findById(new Long(quiz.directionId));
                        quiz.directions = directionsMst.directions;
                    }
                }
                if (springSecurityService.currentUser != null) {
                    testsMst = new TestsMst(createdBy: springSecurityService.currentUser.username, name: (params.testName != null && params.testName.length() > 0) ? params.testName : "Created Test")
                    testsMst.save(failOnError: true, flush: true)
                    if (params.batchId != null && params.batchId.length() > 0) {
                        TestsShared testsShared = new TestsShared(testId: testsMst.id, name: (params.testName != null && params.testName.length() > 0) ? params.testName : "Created Test",
                                batchId: new Long(params.batchId), createdBy: springSecurityService.currentUser.username)
                        testsShared.save(failOnError: true, flush: true)
                    }
                }
            }
            session['quizquestionanswers'] = questions;
            int noOfAnswers = 0;
            boolean languageFound = false
            def quizChapterId
            List jsonQuestions = questions.collect { quiz ->
                noOfAnswers = 0;

                //logic to see if any of the questions has multiple language for create test question
                if ("true".equals(params.createtest) && !languageFound) {
                    if (quiz.question.indexOf("~~") > -1) {
                        languageFound = true
                        resourceDtl = ResourceDtl.findByResLink("" + quiz.quizId)
                        if (resourceDtl.language1 != null) language1 = resourceDtl.language1
                        if (resourceDtl.language1 != null) language2 = resourceDtl.language2
                    }
                }

                if (quiz.answer1 == "Yes") noOfAnswers++; if (quiz.answer2 == "Yes") noOfAnswers++; if (quiz.answer3 == "Yes") noOfAnswers++; if (quiz.answer4 == "Yes") noOfAnswers++;
                if ("true".equals(params.createtest) && springSecurityService.currentUser != null) {
                    TestsDtl testsDtl = new TestsDtl(testId: testsMst.id, objId: quiz.id)
                    testsDtl.save(failOnError: true, flush: true)
                }
                quizChapterId = chaptersMap.get(""+quiz.quizId)
                return [id     : quiz.id, ps: quiz.question, op1: quiz.option1, op2: quiz.option2, op3: quiz.option3, op4: quiz.option4, op5: quiz.option5,
                        resType: quiz.quizType, optionType: (noOfAnswers == 1) ? "radio" : "checkbox", directions: quiz.directions, section: quiz.section,
                        quizId : ("true".equals(params.createtest) && springSecurityService.currentUser != null) ? testsMst.id : quiz.quizId,
                        subject: quiz.subject, marks: quiz.marks, negativeMarks: quiz.negativeMarks, chapterId:quizChapterId]
            }

            def json =
                    [
                            'results'       : jsonQuestions,
                            'status'        : jsonQuestions ? "OK" : "Nothing present",
                            'isPassage'     : isPassage,
                            'passage'       : passage,
                            'description'   : description,
                            'chaptersList'  : chaptersList,
                            'testgenid'     : ("true".equals(params.createtest) && springSecurityService.currentUser != null) ? testsMst.id : null,
                            'language1'     : (resourceDtl != null) ? resourceDtl.language1 : language1,
                            'language2'     : (resourceDtl != null) ? resourceDtl.language2 : language2,
                            'examSubject'   : (resourceDtl != null) ? resourceDtl.examSubject : "",
                            'examMst'       : examMst,
                            'examDtl'       : examDtl,
                            'testSeries'    : testSeries,
                            'testEndDate'   : testEndDate,
                            'testEndDateWeb': testEndDateWeb
                    ]
            render json as JSON
        }else{
            def json = [status: "Nothing present"]
            render json as JSON
        }
    }

    @Transactional
    def getSharedQuizQuestions(){
        List testDtl = TestsDtl.findAllByTestId(new Long(params.testId))
        def objIds = []
        testDtl.each{test->
            objIds << test.objId
        }
        List questions = ObjectiveMst.findAllByIdInList(objIds)
        session['quizquestionanswers'] = questions;
        int noOfAnswers = 0;

        List jsonQuestions = questions.collect { quiz ->
            noOfAnswers = 0;
            if (quiz.answer1 == "Yes") noOfAnswers++; if (quiz.answer2 == "Yes") noOfAnswers++; if (quiz.answer3 == "Yes") noOfAnswers++; if (quiz.answer4 == "Yes") noOfAnswers++;
            if("true".equals(params.createtest)&&springSecurityService.currentUser!=null){
                TestsDtl testsDtl =  new TestsDtl(testId: testsMst.id,objId: quiz.id)
                testsDtl.save(failOnError: true, flush: true)
            }
            return [id         : quiz.id, ps: quiz.question, op1: quiz.option1, op2: quiz.option2, op3: quiz.option3, op4: quiz.option4, op5: quiz.option5,
                    resType    : quiz.quizType, optionType: (noOfAnswers == 1) ? "radio" : "checkbox", directions: quiz.directions, section: quiz.section,
                    quizId: params.testId, subject: quiz.subject,marks:quiz.marks,negativeMarks: quiz.negativeMarks]
        }
        def json =
                [
                        'results'    : jsonQuestions,
                        'status'     : jsonQuestions ? "OK" : "Nothing present",
                        'isPassage'  : false,
                        'passage'    : "",
                        'description': "",
                        'chaptersList' : null,
                        'testgenid' : params.testId
                ]
        render json as JSON
    }

    @Transactional
    def getSharedQuizQuestionAnswers(){
        List testDtl = TestsDtl.findAllByTestId(new Long(params.testId))
        def objIds = []
        testDtl.each{test->
            objIds << test.objId
        }
        List questions = ObjectiveMst.findAllByIdInList(objIds)
        session['quizquestionanswers'] = questions;
        int noOfAnswers = 0;

        List jsonQuestions = questions.collect { quiz ->
            noOfAnswers = 0;
            if (quiz.answer1 == "Yes") noOfAnswers++; if (quiz.answer2 == "Yes") noOfAnswers++; if (quiz.answer3 == "Yes") noOfAnswers++; if (quiz.answer4 == "Yes") noOfAnswers++;
            if("true".equals(params.createtest)&&springSecurityService.currentUser!=null){
                TestsDtl testsDtl =  new TestsDtl(testId: testsMst.id,objId: quiz.id)
                testsDtl.save(failOnError: true, flush: true)
            }
            return [id         : quiz.id, ps: quiz.question, op1: quiz.option1, op2: quiz.option2, op3: quiz.option3, op4: quiz.option4, op5: quiz.option5,
                    resType    : quiz.quizType, optionType: (noOfAnswers == 1) ? "radio" : "checkbox",  ans1: quiz.answer1, ans2: quiz.answer2, ans3: quiz.answer3, ans4: quiz.answer4, ans5: quiz.answer5,
                    directions: quiz.directions, section: quiz.section, answerDescription: quiz.answerDescription, answer: (quiz.answer!=null? new String(quiz.answer, "UTF-8"):""),subject:quiz.subject,
                    chapterId: quiz.chapter, quizId: params.testId, subject: quiz.subject,marks:quiz.marks,negativeMarks: quiz.negativeMarks]

        }
        def json =
                [
                        'results'    : jsonQuestions,
                        'status'     : jsonQuestions ? "OK" : "Nothing present",
                        'isPassage'  : false,
                        'passage'    : "",
                        'description': "",
                        'chaptersList' : null,
                        'testgenid' : params.testId
                ]
        render json as JSON
    }

    def getFlashCardAsMCQQuestions(){
        ResourceDtl documentInstance = ResourceDtl.get(new Long(params.resId))
        if (canSeeResource(documentInstance)) {
            List keyValues = KeyValues.findAllByResIdAndStatus(new Long(params.resId), "active");
            String[] allAnswers = new String[keyValues.size()]
            int index=0;
            keyValues.each { keyValue->
                allAnswers[index] = keyValue.term
                index++;
            }
            int noOfOptions=4;
            if(keyValues.size()<4) noOfOptions=keyValues.size();
            Random rand = new Random()
            String[] answerKeys = new String[4]
            List jsonAnswers = keyValues.collect{ keyValue ->

                int answerPosition =  rand.nextInt(noOfOptions);
                answerKeys[answerPosition] = keyValue.term
                int wrongAnswerPosition;
                Set answers = new HashSet();
                answers.add(""+keyValue.term)
                for(int i=0;i<noOfOptions;i++){
                    if(i==answerPosition) continue
                    boolean wrongAnswerFound=false
                    while(!wrongAnswerFound){
                        wrongAnswerPosition  = rand.nextInt(keyValues.size())
                        if(!answers.contains(""+allAnswers[wrongAnswerPosition])){
                            wrongAnswerFound=true
                            answerKeys[i] = allAnswers[wrongAnswerPosition]
                            answers.add(allAnswers[wrongAnswerPosition])
                        }

                    }


                }
                return [id         : keyValue.id, ps: keyValue.definition, op1: answerKeys[0], op2: answerKeys[1],
                        op3: answerKeys[2], op4: answerKeys[3], op5: null,
                        resType    : 'Multiple Choice Questions', optionType: "radio" , directions: null, section: null,
                        quizId: params.resId, ans1: (answerPosition==0)?"Yes":"", ans2: (answerPosition==1)?"Yes":"",
                        ans3: (answerPosition==2)?"Yes":"", ans4: (answerPosition==3)?"Yes":"", ans5: (answerPosition==4)?"Yes":""]

            }
            def json =
                    [
                            'results'    : jsonAnswers,
                            'status'     : jsonAnswers ? "OK" : "Nothing present",
                            'isPassage'  : false,
                            'passage'    : null,
                            'description': "",
                            'chaptersList' : null,
                            'testgenid' : null
                    ]
            render json as JSON
        }else{
            def json = ["keyValues": null]
            render json as JSON
        }
    }

    def getFlashCardAsTFQuestions(){
        ResourceDtl documentInstance = ResourceDtl.get(new Long(params.resId))
        if (canSeeResource(documentInstance)) {
            List keyValues = KeyValues.findAllByResIdAndStatus(new Long(params.resId), "active");
            String[] allAnswers = new String[keyValues.size()]
            int index=0;
            keyValues.each { keyValue->
                allAnswers[index] = keyValue.term
                index++;
            }
            Random rand = new Random()
            String[] answerKeys = new String[4]
            String question
            String answer1
            boolean gotWrongAnswer
            List jsonAnswers = keyValues.collect{ keyValue ->

                int answerType =  rand.nextInt(2);

                if(answerType==0){
                    //true case
                    answer1="true"
                    question  = keyValue.term+" - "+keyValue.definition

                }else{
                    //false case
                    gotWrongAnswer=false
                    answer1="false"
                    while(!gotWrongAnswer) {
                        int wrongAnswerPostion = rand.nextInt(keyValues.size())
                        if(!keyValue.term.equals(allAnswers[wrongAnswerPostion])){
                            gotWrongAnswer=true
                            question  = allAnswers[wrongAnswerPostion]+" - "+keyValue.definition
                        }
                    }
                }
                return [id         : keyValue.id, ps: question, op1: "True", op2: "False",
                        op3: null, op4: null, op5: null,
                        resType    : 'Multiple Choice Questions', optionType: "radio" , directions: null, section: null,
                        quizId: params.resId, ans1: (answerType==0)?"Yes":"", ans2: (answerType==1)?"Yes":"",
                        ans3: "", ans4:"", ans5: ""]

            }
            def json =
                    [
                            'results'    : jsonAnswers,
                            'status'     : jsonAnswers ? "OK" : "Nothing present",
                            'isPassage'  : false,
                            'passage'    : null,
                            'description': "",
                            'chaptersList' : null,
                            'testgenid' : null
                    ]
            render json as JSON
        }else{
            def json = ["keyValues": null]
            render json as JSON
        }
    }

    def firstTwoQuizQuestions() {
        ResourceDtl resourceDtl = ResourceDtl.findByResLink(params.quizLink)
        if (canSeeResource(resourceDtl)) {
            List questions = ObjectiveMst.findAllByQuizId(new Integer(params.quizLink), [sort: "id", max: 2])
            int noOfAnswers = 0;
            List jsonQuestions = questions.collect { quiz ->
                noOfAnswers = 0;
                if (quiz.answer1 == "Yes") noOfAnswers++; if (quiz.answer2 == "Yes") noOfAnswers++; if (quiz.answer3 == "Yes") noOfAnswers++; if (quiz.answer4 == "Yes") noOfAnswers++;
                return [id: quiz.id, ps: quiz.question, op1: quiz.option1, op2: quiz.option2, op3: quiz.option3, op4: quiz.option4, resType: quiz.quizType, optionType: (noOfAnswers == 1) ? "radio" : "checkbox", fileName: quiz.questionFilename]
            }
            def json =
                    [
                            'results' : jsonQuestions,
                            'quizId'  : params.quizId,
                            'quizLink': params.quizLink,
                            'status'  : jsonQuestions ? "OK" : "Nothing present"
                    ]
            render json as JSON
        }

    }

    def quizAnswers() {
        List answers = (List) session['quizquestionanswers'];
        int noOfAnswers = 0;
        ExamMst examMst = null
        List examDtl = null
        def quizId = null;
        List jsonAnswers = answers.collect { quiz ->
            noOfAnswers = 0;
            if (quiz.answer1 == "Yes") noOfAnswers++; if (quiz.answer2 == "Yes") noOfAnswers++; if (quiz.answer3 == "Yes") noOfAnswers++; if (quiz.answer4 == "Yes") noOfAnswers++;
            quizId = quiz.quizId
            return [id         : quiz.id, ps: quiz.question, op1: quiz.option1, op2: quiz.option2, op3: quiz.option3, op4: quiz.option4, op5: quiz.option5,
                    resType    : quiz.quizType, optionType: (noOfAnswers == 1) ? "radio" : "checkbox", ans1: quiz.answer1, ans2: quiz.answer2, ans3: quiz.answer3, ans4: quiz.answer4, ans5: quiz.answer5,
                    directions: quiz.directions, section: quiz.section,
                    answerDescription: quiz.answerDescription, answer: (quiz.answer!=null? new String(quiz.answer, "UTF-8"):""),subject:quiz.subject,
                    chapterId: quiz.chapter, quizId: quiz.quizId, marks:quiz.marks,negativeMarks: quiz.negativeMarks,explainLink: quiz.explainLink,startTime: quiz.startTime,endTime: quiz.endTime]
        }

        ResourceDtl resourceDtl = ResourceDtl.findByResLink(""+quizId)
        if(resourceDtl!=null && resourceDtl.examId!=null ) {
            examMst = ExamMst.findById(resourceDtl.examId)
            if(examMst!=null) examDtl = ExamDtl.findAllByExamId(examMst.id)
        }

        def json =
                [
                        'results': jsonAnswers,
                        'status' : jsonAnswers ? "OK" : "Nothing present",
                        examMst: examMst,
                        examDtl: examDtl
                ]
        render json as JSON

    }

    @Transactional

    def newQuizQA(){
        ResourceDtl resourceDtl;
        boolean canAccess = true;
        String isPassage = "false";
        String passage = "";
        String description;
        List chaptersList=null;
        ExamMst examMst = null
        def examDtl = null
        String testSeries="false";
        def testEndDate=null
        def testEndDateTimer=null
        String chapterName;

        if (params.inputDate != null) {
            if (redisService.("currentAffairsQuiz_" + params.inputDate) == null) adminService.getCurrentAffairsQuizId(params.inputDate)
            if (!"-1".equals(redisService.("currentAffairsQuiz_" + params.inputDate)))
                resourceDtl = ResourceDtl.findById(new Integer(redisService.("currentAffairsQuiz_" + params.inputDate)))
        }
        else if(params.resId!=null){
            resourceDtl = dataProviderService.getResourceDtl(new Long(params.resId))
        }
        else {
            List resourceDtls = ResourceDtl.findAllByResLink(params.quizId)
            canAccess = false;

            resourceDtls.each { resDtl ->
                if (resDtl.quizMode!=null||canSeeResource(resDtl)) {
                    canAccess = true
                    resourceDtl = resDtl
                } else {
                    println("the id not having access is=" + resDtl.id)
                }
            }
            if (!canAccess) {
                resourceDtl = resourceDtls[0]
            }
        }
            if (resourceDtl!=null&&canAccess) {
            def resourceName = resourceDtl.resourceName
            description = resourceDtl.description
                if (resourceDtl.examId != null) {
                    examMst = ExamMst.findById(resourceDtl.examId)
                    if (examMst != null) {
                        if(redisService.("examDtl_" +examMst.id)==null) dataProviderService.getExamDtls(examMst.id)
                        examDtl = redisService.("examDtl_" +examMst.id)
                    }
                }

            if (resourceDtl.examSyllabus != null && !"".equals(resourceDtl.examSyllabus)) chaptersList = ExamChapterMst.findAllByExam(resourceDtl.examSyllabus)
            if (canAccess) {


                if ("passage".equals(resourceDtl.quizMode)) {
                    isPassage = "true";
                    passage = resourceDtl.chapterDesc;
                }
                if (resourceDtl.chapterId != null) {
                    ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)
                    chapterName = chaptersMst.name
                    if (resourceDtl.testStartDate != null && resourceDtl.testEndDate != null) {
                        testSeries = "true"
                        Date currentDate = new Date()
                        long timeDifferenceInMillis = resourceDtl.testEndDate.getTime() - currentDate.getTime()
                        long timeDifferenceInMinutes = timeDifferenceInMillis / (1000 * 60)
                        testEndDateTimer = timeDifferenceInMinutes > 0 ? timeDifferenceInMinutes : 0

                        testEndDate = utilService.convertDate(resourceDtl.testEndDate, "UTC", "IST")
                    }

                }
            }

            if(params.fromLiveMockTest && "true".equals(params.fromLiveMockTest)){
                canAccess = true
            }


            def quizRecorder = null

            if(redisService.("quiz_"+resourceDtl.id) ==null||"null".equals(redisService.("quiz_"+resourceDtl.id))) {
                dataProviderService.quizDetails(resourceDtl)
            }
            def  jsonAnswers = redisService.("quiz_"+resourceDtl.id)

                if (params.noOfQuestions) {
                    def jsonSlurper = new JsonSlurper()
                    def jsonObject = jsonSlurper.parseText(jsonAnswers)
                    if(!"71".equals(""+session["siteId"])){
                        Collections.shuffle(jsonObject)
                    }
                    if (params.difficultyLevel && !params.difficultyLevel.equals("All")) {
                        String difficultyLevel = params.difficultyLevel
                        jsonObject = jsonObject.findAll { it.difficultyLevel == difficultyLevel }
                        //if you do not find the questions for the given difficulty level
                        if(jsonObject.size()==0) jsonObject = jsonSlurper.parseText(jsonAnswers)
                    }

                    int noOfQuestions = params.noOfQuestions.toInteger()
                    jsonObject = jsonObject.take(noOfQuestions)
                    Gson gson = new Gson();
                   jsonAnswers = gson.toJson(jsonObject, new TypeToken<List>() {}.getType())
                }
            def json =
                    [
                            'results'     : jsonAnswers,
                            'status'      : jsonAnswers ? "OK" : "Nothing present",
                            'isPassage'   : isPassage,
                            'passage'     : passage,
                            'description' : description,
                            'resourceName': resourceName,
                            'chaptersList': chaptersList,
                            'language1'   : (resourceDtl != null) ? resourceDtl.language1 : "",
                            'language2'   : (resourceDtl != null) ? resourceDtl.language2 : "",
                            'examSubject' : (resourceDtl != null) ? resourceDtl.examSubject : "",
                            'examMst'     : examMst,
                            'examDtl'     : examDtl,
                            'testSeries'  : testSeries,
                            'quizRecorder': quizRecorder,
                            'testEndDate' : testEndDate,
                            'chapterName' : chapterName,
                            'challengerName' : prepjoyService.getName(),
                            'challengerPlace' : prepjoyService.getPlace(),
                            'mcqTotalTime': (resourceDtl != null) ? resourceDtl.mcqTotalTime: null,
                            'testEndDateTimer': testEndDateTimer

                    ]



            render json as JSON
        }else{
            def json = ['status':'Nothing Present']
            render json as JSON
        }
    }
    @Transactional
    def quizQuestionAnswers() {
        ResourceDtl resourceDtl;
        def sql
        List answers
        boolean canAccess = true;
        String isPassage = "false";
        String passage = "";
        String description;
        String canReorderQuestions;
        String resourceName="";
        List chaptersList=null;
        ExamMst examMst = null
        List examDtl = null
        String testSeries="false";
        def testEndDate=null
        String chapterName;
        HashMap chaptersMap =  new HashMap()
        HashSet subjects = new HashSet()

        if ("true".equals(params.createtest)) {
            canAccess=true
            if (canAccess) {
                sql = "select res_link,chapter_id,cm.name chapterName,bm.title from resource_dtl rd,chapters_mst cm,books_mst bm  where res_type='" + params.resType + "' and chapter_id in (" + params.chaptersList + ")  and cm.id=rd.chapter_id\n" +
                        "  and bm.id=cm.book_id";
                def dataSource = grailsApplication.mainContext.getBean('dataSource')
                def sql1 = new Sql(dataSource)
                def quizIdsList = sql1.rows(sql);
                def quizIds = [];
                def objIds = [];
                String stringQuizIds="";
                quizIdsList.each { quiz ->
                    quizIds << new Integer("" + quiz[0])
                    stringQuizIds +=quiz[0]+","
                    chaptersMap.put(""+quiz[0],""+quiz[1])

                }
                stringQuizIds = stringQuizIds.substring(0,stringQuizIds.length()-1)
                sql = "SELECT t1.id FROM objective_mst AS t1 JOIN (SELECT id FROM objective_mst  where quiz_id in" +
                        " ("+stringQuizIds+") ORDER BY RAND() LIMIT "+params.noOfQuestions+") as t2 ON t1.id=t2.id"
                dataSource = grailsApplication.mainContext.getBean('dataSource')
                sql1 = new Sql(dataSource)
                def quizList = sql1.rows(sql);
                quizList.each { quiz ->
                    objIds << new Integer("" + quiz[0])
                }

                answers = ObjectiveMst.findAllByIdInList(objIds)

                chaptersList = quizIdsList.collect { comp ->
                    return [quizId: comp[0], id: comp[1], name: comp[2], subject: comp[3]]
                }

            }
        } else if("true".equals(params.favouriteMCQs)){
            if(params.subject!=null)
                sql = "select obj_id,subject from favouritemcqs where username='"+springSecurityService.currentUser.username+"' and subject='"+params.subject+"'";
            else sql = "select obj_id,subject from favouritemcqs where username='"+springSecurityService.currentUser.username+"'";
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
            def sql1 = new Sql(dataSource)
            def quizList = sql1.rows(sql);
            def objIds = [];
            quizList.each { quiz ->
                objIds << new Integer("" + quiz[0])
                subjects.add(""+quiz[1])
            }

            answers = ObjectiveMst.findAllByIdInList(objIds)

            canAccess = true

        }

        else {
            if (params.inputDate != null) {
                if (redisService.("currentAffairsQuiz_" + params.inputDate) == null) adminService.getCurrentAffairsQuizId(params.inputDate)
                if (!"-1".equals(redisService.("currentAffairsQuiz_" + params.inputDate)))
                    resourceDtl = ResourceDtl.findById(new Integer(redisService.("currentAffairsQuiz_" + params.inputDate)))
            } else if (params.resId != null) {
                resourceDtl = dataProviderService.getResourceDtl(new Long(params.resId))
            } else {
                List resourceDtls = ResourceDtl.findAllByResLink(params.quizId)
                canAccess = false;

                resourceDtls.each { resDtl ->
                    if (resDtl.quizMode != null || canSeeResource(resDtl)) {
                        canAccess = true
                        resourceDtl = resDtl
                    } else {
                        println("the id not having access is=" + resDtl.id)
                    }
                }
                if (!canAccess) {
                    resourceDtl = resourceDtls[0]
                }
            }
            if (resourceDtl != null) {
                resourceName = resourceDtl.resourceName
                description = resourceDtl.description
                canReorderQuestions = resourceDtl.canReorderQuestions
                if (resourceDtl.examId != null) {
                    examMst = ExamMst.findById(resourceDtl.examId)
                    if (examMst != null) examDtl = ExamDtl.findAllByExamId(examMst.id)
                }

                if (resourceDtl.examSyllabus != null && !"".equals(resourceDtl.examSyllabus)) chaptersList = ExamChapterMst.findAllByExam(resourceDtl.examSyllabus)
                if (canAccess) {
                    chaptersMap.put(""+resourceDtl.resLink,""+resourceDtl.chapterId)
                    def quizId = params.quizId
                    String sort=""
                    if (quizId == null || "null".equals(quizId)) {
                        quizId = resourceDtl.resLink
                    }
                    ObjectiveMst objectiveMst = ObjectiveMst.findByQuizIdAndQuizSort(new Integer(resourceDtl.resLink),0)
                    if(objectiveMst != null) sort = "quizSort"
                    else sort = "id"
                    answers = ObjectiveMst.findAllByQuizId(new Integer(quizId), [sort: sort])
                    // logic to remove expired questions
                    for (Iterator<String> iter = answers.listIterator(); iter.hasNext();) {
                        ObjectiveMst question = iter.next();
                        if (question.expiryDate != null && question.expiryDate.compareTo(new Date()) < 0) {
                            iter.remove()
                        }
                    }
                    if ("passage".equals(resourceDtl.quizMode)) {
                        isPassage = "true";
                        passage = resourceDtl.chapterDesc;
                    }
                    if (resourceDtl.chapterId != null) {
                        ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)
                        chapterName = chaptersMst.name
                        BooksMst booksMst = dataProviderService.getBooksMst(chaptersMst.bookId)
                        if (resourceDtl.testStartDate != null && resourceDtl.testEndDate != null) {
                            testSeries = "true"
                            testEndDate = testEndDate = utilService.convertDate(resourceDtl.testEndDate, "UTC", "IST")
                        }
                        if ("test".equals(booksMst.bookType)) {
                            testSeries = "true"
                            testEndDate = booksMst.testEndDate
                        };
                    }
                }
            }
        }

        if (canAccess && ("true".equals(params.createtest) || (resourceDtl!=null)||("true".equals(params.favouriteMCQs)))) {
            def quizRecorder=null
            long seed = System.nanoTime();
            if ("true".equals(canReorderQuestions)) Collections.shuffle(answers, new Random(seed));
            if ("true".equals(params.createtest)) {
                Collections.shuffle(answers, new Random(seed));
                int extraQuestions = answers.size() - Integer.parseInt(params.noOfQuestions);

                for (int i = 0; i < extraQuestions; i++) answers.remove((answers.size() - 1));

            }
            session['quizquestionanswers'] = answers;
            String directions, section
            int noOfAnswers = 0;
            def quizChapterId
            List jsonAnswers = answers.collect { quiz ->
                noOfAnswers = 0;
                if (quiz.answer1 == "Yes") noOfAnswers++; if (quiz.answer2 == "Yes") noOfAnswers++; if (quiz.answer3 == "Yes") noOfAnswers++; if (quiz.answer4 == "Yes") noOfAnswers++;

                if ("true".equals(params.createtest)) {
                    directions = "";
                    section = "";
                    if (quiz.directionId != null) {
                        DirectionsMst directionsMst = DirectionsMst.findById(new Long(quiz.directionId));
                        directions = directionsMst.directions;
                    }
                } else {
                    directions = quiz.directions
                    section = quiz.section
                }
                quizChapterId = chaptersMap.get(""+quiz.quizId)
                return [id         : quiz.id, ps: quiz.question, op1: quiz.option1, op2: quiz.option2, op3: quiz.option3, op4: quiz.option4, op5: quiz.option5,
                        resType    : quiz.quizType, optionType: (noOfAnswers == 1) ? "radio" : "checkbox",  ans1: quiz.answer1, ans2: quiz.answer2, ans3: quiz.answer3, ans4: quiz.answer4, ans5: quiz.answer5,
                        directions: quiz.directions, section: quiz.section,
                        answerDescription: quiz.answerDescription, answer: quiz.answer!=null? quiz.answer:"",subject:quiz.subject,
                        chapterId: quiz.chapter, quizId: quiz.quizId,marks:quiz.marks,negativeMarks: quiz.negativeMarks,explainLink: quiz.explainLink,
                        startTime: quiz.startTime,endTime: quiz.endTime, chapterId:quizChapterId]
            }
            if(params.quizRecorderId!=null&&!"".equals(params.quizRecorderId)){
                quizRecorder = Quizrecorder.findById(new Long(params.quizRecorderId))
                quizRecorder.userAnswers = getUserAnswers(quizRecorder.id)
            }
            def json =
                    [
                            'results'    : jsonAnswers,
                            'status'     : jsonAnswers ? "OK" : "Nothing present",
                            'isPassage'  : isPassage,
                            'passage'    : passage,
                            'description': description,
                            'resourceName':resourceName,
                            'chaptersList' : chaptersList,
                            'language1' : (resourceDtl!=null)?resourceDtl.language1:"",
                            'language2' : (resourceDtl!=null)?resourceDtl.language2:"",
                            'examSubject' : (resourceDtl!=null)?resourceDtl.examSubject:"",
                            'examMst' : examMst,
                            'examDtl' : examDtl,
                            'testSeries':testSeries,
                            'quizRecorder':quizRecorder,
                            'testEndDate':testEndDate,
                            'testEndDateTimer':testEndDateTimer,
                            'chapterName':chapterName,
                            'challengerName' : prepjoyService.getName(),
                            'challengerPlace' : prepjoyService.getPlace(),
                            'subject':subjects

                    ]
            render json as JSON
        }
        else{
            def json = ['status':'Nothing Present']
            render json as JSON
        }
    }
    @Transactional
    def getUserAnswers(quizRecorderId)
    {

        String userAnswers = null

        List qrdtls = Quizrecorderdtl.findAllByQuizrecorderid(quizRecorderId)

        if(qrdtls.size()>0) userAnswers = "["

        qrdtls.each{ qrdtl ->
            userAnswers += "{\"skipped\":"+("skipped".equals(qrdtl.correctanswer)?"\"true\"":"\"false\"")+
                    ",\"ans1\":"+(qrdtl.option1!=null&&!"null".equals(qrdtl.option1)?"\""+qrdtl.option1+"\",":"null,")+
                    "\"ans2\":"+(qrdtl.option2!=null&&!"null".equals(qrdtl.option2)?"\""+qrdtl.option2+"\",":"null,")+
                    "\"ans3\":"+(qrdtl.option3!=null&&!"null".equals(qrdtl.option3)?"\""+qrdtl.option3+"\",":"null,")+
                    "\"ans4\":"+(qrdtl.option4!=null&&!"null".equals(qrdtl.option4)?"\""+qrdtl.option4+"\",":"null,")+
                    "\"ans5\":"+(qrdtl.option5!=null&&!"null".equals(qrdtl.option5)?"\""+qrdtl.option5+"\",":"null,")+
                    "\"correctAnswer\":"+("skipped".equals(qrdtl.correctanswer)?"\"false\",":"\""+qrdtl.correctanswer+"\",")+
                    "\"id\":"+qrdtl.objectivemstid+"},"



        }
        if(qrdtls.size()>0){
            userAnswers = userAnswers.substring(0,userAnswers.length()-1)+"]"
        }

        return userAnswers
    }

    @Transactional @Secured(['ROLE_USER'])
    def getUserAnswerDetails(){

        String quizRecorderId = params.quizRecorderId


        def json = ["answers":getUserAnswers(new Integer(quizRecorderId))]
        render json as JSON

    }
    @Transactional @Secured(['ROLE_USER'])
    def getUserTests(){

        String quizId = params.quizId

        List userQuizList = Quizrecorder.findAllByUsernameAndQuizid(springSecurityService.currentUser.username,new Integer(quizId), [sort: "id"])

        List quizList = userQuizList.collect { quiz ->
            return [quizRecorderId: quiz.id, quizTakenTime: quiz.quizTakenTime, rank:""+quiz.rank,
                    score:""+quiz.score,timeTaken:""+quiz.quizTakenTime,totalQuestions:""+quiz.totalQuestions, correctAnswers:""+quiz.correctAnswers,
                    wrongAnswers:""+quiz.wrongAnswers,skipped: ""+quiz.skipped]
        }
        def json = ["quizList":quizList]
        render json as JSON

    }
    def topic() {
        if (springSecurityService.currentUser != null && session.getAttribute("userdetails") == null) {
            session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)
        }

        if ((params.topicId == null || ''.equals(params.topicId)) && (params.id == null || ''.equals(params.id))) redirect(action: 'index')
        else {
            def topicId
            def resType
            def link
            if (params.id != null && !''.equals(params.topicId)) {
                ResourceDtl rd = ResourceDtl.findById(new Integer(params.id))
                topicId = rd.topicId
                resType = rd.resType
                link = rd.resLink
            }

            TopicMst topicMst = TopicMst.findById(new Integer(params.topicId == null || ''.equals(params.topicId) ? topicId : params.topicId))
            Cookie cookie = new Cookie("syllabus", topicMst.syllabus)
            cookie.maxAge = 99999999
            cookie.setPath("/");
            response.addCookie(cookie)
            cookie = new Cookie("grade", "" + topicMst.grade)
            cookie.setPath("/");
            cookie.maxAge = 99999999
            response.addCookie(cookie)
            cookie = new Cookie("subject", topicMst.subject)
            cookie.setPath("/");
            cookie.maxAge = 99999999
            response.addCookie(cookie)
            cookie = new Cookie("level", topicMst.syllabusType)
            cookie.setPath("/");
            cookie.maxAge = 99999999
            response.addCookie(cookie)

            def keywords = topicMst.syllabus + " ,mind maps, videos , quiz, solved question paper," + topicMst.syllabus + " " + topicMst.topicName + ", " + topicMst.topicName + " " + topicMst.grade

            [topicId : (topicMst.parentId == null) ? topicMst.id : topicMst.parentId,
             topicMst: topicMst, displayName: topicMst.topicName,
             giveAdd : "true", title: topicMst.topicName, keywords: keywords, showDiscover: "true",
             id      : params.id,
             resType : resType,
             link    : link
            ]
        }

    }

    def book() {
        if (springSecurityService.currentUser != null && session.getAttribute("userdetails") == null) {
            session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)
        }

        if ((params.bookId == null || ''.equals(params.bookId))) redirect(action: 'index')
        else {
            def chapterId
            def resType
            def link

            BooksMst booksMst = BooksMst.findById(new Long(params.bookId));
            List chaptersMst = ChaptersMst.findAllByBookId(new Long(params.bookId))

            def keywords = booksMst.title + " on Wonderslate";

            [topicId  : chaptersMst[0].id,
             topicMst : chaptersMst, displayName: booksMst.title,
             title    : booksMst.title, keywords: keywords,
             id       : params.id,
             resType  : resType,
             link     : link,
             booksPage: "true"
            ]
        }
    }

    def topicDetails() {
        if (params.topicId == null || ''.equals(params.topicId)) redirect(action: 'index')
        else {
            TopicMst topicMst = TopicMst.findById(new Integer(params.topicId))
            render topicMst as JSON
        }
    }

    def topicsMap() {

        String country = "India"
        if (params.country != null && !"".equals(params.country)) country = params.country

        String sql

        if (springSecurityService.currentUser == null) {

            sql = "select tm.syllabus_type,tm.syllabus,gm.grade,tm.subject,tm.topic_name,tm.id,sm.sort_by sm_sort_by,gm.sort_by gm_sort_by,lm.sort_by lm_sort_by from topic_mst tm,syllabus_mst sm,grade_mst gm,levels_mst lm  " +
                    " where tm.id in (select distinct topic_id from resource_dtl where sharing='public') and tm.syllabus = sm.name and tm.grade=gm.grade " +
                    " and sm.country='" + country + "' and tm.syllabus_type=sm.syllabus_type and tm.country=sm.country" +
                    " and tm.syllabus_type=gm.syllabus_type and gm.country=sm.country" +
                    " and lm.name=sm.syllabus_type and lm.country=sm.country" +
                    " union " +
                    "select tm.syllabus_type,tm.syllabus,gm.grade,tm.subject,tm.topic_name,tm.id,sm.sort_by sm_sort_by,gm.sort_by gm_sort_by,lm.sort_by lm_sort_by from topic_mst tm,syllabus_mst sm,grade_mst gm,levels_mst lm  " +
                    " where tm.can_be_displayed='true' and tm.syllabus = sm.name and tm.grade=gm.grade " +
                    " and sm.country='" + country + "' and tm.syllabus_type=sm.syllabus_type and tm.country=sm.country" +
                    " and tm.syllabus_type=gm.syllabus_type and gm.country=sm.country" +
                    " and lm.name=sm.syllabus_type and lm.country=sm.country" +
                    " order by lm_sort_by,sm_sort_by,syllabus,gm_sort_by,subject,topic_name"

        } else {
            // all public union, all topic created by user union  all resources created by user union all topic which the user is member of the group in which the resource has been shared.

            if ("mylibrary".equals(params.mode))
                sql = "select tm.syllabus_type,tm.syllabus,gm.grade,tm.subject,tm.topic_name,tm.id,sm.sort_by sm_sort_by,gm.sort_by gm_sort_by,lm.sort_by lm_sort_by from topic_mst tm,syllabus_mst sm,grade_mst gm,levels_mst lm  " +
                        " where tm.id in (select distinct topic_id from resource_dtl rd, wslog.resource_view rv where rv.username ='" + springSecurityService.currentUser.username + "' and (rd.sharing is  null or rd.sharing !='deleted') and rv.resource_dtl_id=rd.id and rv.action='favourite')" +
                        " and tm.syllabus = sm.name and tm.grade=gm.grade" +
                        " and sm.country='" + country + "' and tm.syllabus_type=sm.syllabus_type and tm.country=sm.country" +
                        " and lm.name=sm.syllabus_type and lm.country=sm.country" +
                        " and tm.syllabus_type=gm.syllabus_type and gm.country=sm.country";

            else sql = "select tm.syllabus_type,tm.syllabus,gm.grade,tm.subject,tm.topic_name,tm.id,sm.sort_by sm_sort_by,gm.sort_by gm_sort_by,lm.sort_by lm_sort_by from topic_mst tm,syllabus_mst sm,grade_mst gm,levels_mst lm  " +
                    " where tm.id in (select distinct topic_id from resource_dtl where sharing='public') and tm.syllabus = sm.name and tm.grade=gm.grade " +
                    " and sm.country='" + country + "' and tm.syllabus_type=sm.syllabus_type and tm.country=sm.country" +
                    " and tm.syllabus_type=gm.syllabus_type and gm.country=sm.country" +
                    " and lm.name=sm.syllabus_type and lm.country=sm.country" +
                    " union " +
                    "select tm.syllabus_type,tm.syllabus,gm.grade,tm.subject,tm.topic_name,tm.id,sm.sort_by sm_sort_by,gm.sort_by gm_sort_by,lm.sort_by lm_sort_by from topic_mst tm,syllabus_mst sm,grade_mst gm,levels_mst lm  " +
                    " where tm.can_be_displayed='true' and tm.syllabus = sm.name and tm.grade=gm.grade " +
                    " and sm.country='" + country + "' and tm.syllabus_type=sm.syllabus_type and tm.country=sm.country" +
                    " and tm.syllabus_type=gm.syllabus_type and gm.country=sm.country" +
                    " and lm.name=sm.syllabus_type and lm.country=sm.country"
            ;

            sql += " union " +
                    "select tm.syllabus_type,tm.syllabus,gm.grade,tm.subject,tm.topic_name,tm.id,sm.sort_by sm_sort_by,gm.sort_by gm_sort_by,lm.sort_by lm_sort_by from topic_mst tm,syllabus_mst sm,grade_mst gm,levels_mst lm  " +
                    " where tm.created_by ='" + springSecurityService.currentUser.username + "' and tm.syllabus = sm.name and tm.grade=gm.grade " +
                    " and sm.country='" + country + "' and tm.syllabus_type=sm.syllabus_type and tm.country=sm.country" +
                    " and tm.syllabus_type=gm.syllabus_type and gm.country=sm.country" +
                    " and lm.name=sm.syllabus_type and lm.country=sm.country" +
                    " union " +
                    "select tm.syllabus_type,tm.syllabus,gm.grade,tm.subject,tm.topic_name,tm.id,sm.sort_by sm_sort_by,gm.sort_by gm_sort_by,lm.sort_by lm_sort_by from topic_mst tm,syllabus_mst sm,grade_mst gm,levels_mst lm  " +
                    " where tm.id in (select distinct topic_id from resource_dtl where created_by ='" + springSecurityService.currentUser.username + "' and (sharing is  null or sharing !='deleted')) and tm.syllabus = sm.name and tm.grade=gm.grade " +
                    " and sm.country='" + country + "' and tm.syllabus_type=sm.syllabus_type and tm.country=sm.country" +
                    " and tm.syllabus_type=gm.syllabus_type and gm.country=sm.country" +
                    " and lm.name=sm.syllabus_type and lm.country=sm.country" +
                    " union " +
                    "select tm.syllabus_type,tm.syllabus,gm.grade,tm.subject,tm.topic_name,tm.id,sm.sort_by sm_sort_by,gm.sort_by gm_sort_by,lm.sort_by lm_sort_by from topic_mst tm,syllabus_mst sm,grade_mst gm,levels_mst lm  " +
                    " where tm.id in (select distinct(topic_id) from resource_dtl rd , resource_group_dtl rgd, groups_dtl gd " +
                    "where rgd.resource_id=rd.id and gd.group_id=rgd.group_id and (rd.sharing is  null or rd.sharing !='deleted') and gd.username ='" + springSecurityService.currentUser.username + "') " +
                    "and tm.syllabus = sm.name and tm.grade=gm.grade " +
                    " and sm.country='" + country + "' and tm.syllabus_type=sm.syllabus_type and tm.country=sm.country" +
                    " and tm.syllabus_type=gm.syllabus_type and gm.country=sm.country" +
                    " and lm.name=sm.syllabus_type and lm.country=sm.country" +
                    " order by lm_sort_by,sm_sort_by,syllabus,gm_sort_by,subject,topic_name"
        }

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);



        List topicsList = results.collect { topic ->
            return [level: topic[0], syllabus: topic[1], grade: topic[2], subject: topic[3], topic: topic[4], id: topic[5]]
        }
        def json =
                [
                        'results'       : topicsList,
                        'status'        : topicsList ? "OK" : "Nothing present",
                        'bookCategories': 'false'
                ]
        render json as JSON
    }

    @Transactional
    def test() {
        def requestBody = request.JSON
        render "The type of class is "+requestBody.class
    }


    def download(long id) {
        ResourceDtl documentInstance = ResourceDtl.get(id)
        filenameCheck(documentInstance)
        if (documentInstance == null) {
            flash.message = "Document not found."
            redirect(action: 'list')
        } else {
            if (canSeeResource(documentInstance)) {
                String cdnLink = contentDeliveryService.generateSignedURL(grailsApplication.config.grails.cdn.pdf.path + documentInstance.resLink)
                response.setStatus(HttpServletResponse.SC_MOVED_TEMPORARILY)
                response.setHeader("Location", cdnLink)
            } else {
                redirect(action: 'index')
            }


        }
    }


    def downloadFile(long id) {
        ResourceDtl documentInstance = ResourceDtl.get(id)
        if (documentInstance == null) {
            flash.message = "Document not found."
            redirect(action: 'list')
        } else {
            if (canSeeResource(documentInstance)) {
                def file = new File(documentInstance.resLink)
                response.setContentType("APPLICATION/OCTET-STREAM")
                response.setHeader("Content-Disposition", "Attachment;Filename=\"${documentInstance.filename}\"")
                response.setHeader("Content-Length", "${file.length()}")
                def fileInputStream = new FileInputStream(file)
                def outputStream = response.getOutputStream()
                byte[] buffer = new byte[4096];
                int len;
                while ((len = fileInputStream.read(buffer)) > 0) {
                    outputStream.write(buffer, 0, len);
                }
                outputStream.flush()
                outputStream.close()
                fileInputStream.close()
            }
        }
    }

    def downloadSample() {
        response.setContentType("APPLICATION/OCTET-STREAM")
        response.setHeader("Content-Disposition", "Attachment;Filename=\"Template_${params.resType}.xls\"")
        def file = new File("upload" + File.separator + "templates" + File.separator + "Template_" + params.resType + ".xls")
        def fileInputStream = new FileInputStream(file)
        def outputStream = response.getOutputStream()
        byte[] buffer = new byte[4096];
        int len;
        while ((len = fileInputStream.read(buffer)) > 0) {
            outputStream.write(buffer, 0, len);
        }
        outputStream.flush()
        outputStream.close()
        fileInputStream.close()
    }

    def showProfileImage(long id, String fileName, String type, String imgType) {
        try {
            if (fileName != null && !"null".equals(fileName) && fileName.length() > 0) {
                String picFileName
                def file
                if("webp".equals(imgType)){
                    picFileName = fileName.substring(0, fileName.indexOf(".")) + '.webp'
                    fileName=picFileName
                }else {
                    picFileName = fileName.substring(0, fileName.indexOf(".")) + '_' + imgType + fileName.substring(fileName.indexOf("."))
                }

                String uploadParentDir="upload"
                if("books".equals(type)){
                    BooksMst booksMst = dataProviderService.getBooksMst(new Integer(params.id))
                    if("Yes".equals(booksMst.newStorage)) uploadParentDir="supload"
                }

                file = new File(uploadParentDir+"/" + type + "/" + id + "/processed/" + picFileName)

                if (file.exists()) {
                    response.setContentType("APPLICATION/OCTET-STREAM")
                    response.setHeader("Content-Disposition", "Attachment;Filename=\"${fileName}\"")
                    def fileInputStream = new FileInputStream(file)
                    def outputStream = response.getOutputStream()
                    byte[] buffer = new byte[4096];
                    int len;
                    while ((len = fileInputStream.read(buffer)) > 0) {
                        outputStream.write(buffer, 0, len);
                    }

                    outputStream.flush()
                    outputStream.close()
                    fileInputStream.close()
                } else render "";
            } else render "";
        }
        catch (Exception e)
        {
            println("Exception in showProfileImage "+e.toString())
            render "";

        }
    }

    @Transactional
    def booksImageResizeBySite(){
            List books = BooksMst.findAllBySiteIdAndStatus(new Long(params.siteId), 'published')
            books.each { booksAll ->
                try {
                BooksMst booksMst = BooksMst.findById(new Long(booksAll.id))
                String filename = booksMst.coverImage
                File uploadDir = new File("upload/books/" + booksMst.id)
                if (uploadDir.exists()) {
                    //creating directory to process images
                    File uploadDir1 = new File(uploadDir.absolutePath + "/processed")
                    if (uploadDir1.exists()) {
                        BufferedImage image = ImageIO.read(new File("upload/books/" + booksMst.id + "/" + filename))
                        ByteArrayOutputStream baos = new ByteArrayOutputStream()
                        ImageIO.write(Scalr.resize(image, Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 125, 125, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".") + 1), baos)
                        baos.flush()
                        byte[] scaledImageInByte = baos.toByteArray()
                        baos.close()

                        baos = new ByteArrayOutputStream()
                        ImageIO.write(Scalr.resize(image, Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 500, 500, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".") + 1), baos)
                        baos.flush()
                        byte[] scaledImageInByte1 = baos.toByteArray()
                        baos.close()
                        FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath + "/" + filename.substring(0, filename.indexOf(".")) + '_thumbnail' + filename.substring(filename.indexOf("."))), scaledImageInByte)
                        FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath + "/" + filename.substring(0, filename.indexOf(".")) + '_passport' + filename.substring(filename.indexOf("."))), scaledImageInByte1)
                    }
                }
                render "";
            } catch (Exception e )
            {
                println("Exception in booksImageResizeBySite " + e.toString())
            }
        }
    }

    @Transactional
    def booksImageResizeByBook() {
        try {
            BooksMst booksMst = BooksMst.findById(new Long(params.bookId))
            String filename = booksMst.coverImage
            File uploadDir = new File("upload/books/" + booksMst.id)
            if (uploadDir.exists()) {
                //creating directory to process images
                File uploadDir1 = new File(uploadDir.absolutePath + "/processed")
                if (uploadDir1.exists()) {
                    BufferedImage image = ImageIO.read(new File("upload/books/" + booksMst.id + "/" + filename))
                    ByteArrayOutputStream baos = new ByteArrayOutputStream()
                    ImageIO.write(Scalr.resize(image, Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 125, 125, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".") + 1), baos)
                    baos.flush()
                    byte[] scaledImageInByte = baos.toByteArray()
                    baos.close()

                    baos = new ByteArrayOutputStream()
                    ImageIO.write(Scalr.resize(image, Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 500, 500, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".") + 1), baos)
                    baos.flush()
                    byte[] scaledImageInByte1 = baos.toByteArray()
                    baos.close()
                    FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath + "/" + filename.substring(0, filename.indexOf(".")) + '_thumbnail' + filename.substring(filename.indexOf("."))), scaledImageInByte)
                    FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath + "/" + filename.substring(0, filename.indexOf(".")) + '_passport' + filename.substring(filename.indexOf("."))), scaledImageInByte1)
                }
            }
            render "";
        } catch (Exception e)
        {
            println("Exception in booksImageResizeByBook "+e.toString())
            render "";

        }
    }

    def imageOptimizer(){
        BooksMst booksMst = BooksMst.findById(new Long(params.bookId))
        String filename = booksMst.coverImage
        File uploadDir = new File("upload/books/" + booksMst.id)
        //JPEG, JPG
        File uploadDir1 = new File(uploadDir.absolutePath + "/processed")
        BufferedImage image = ImageIO.read(new File("upload/books/" + booksMst.id + "/" + filename));
        ImageWriter writer = ImageIO.getImageWritersByFormatName("jpeg").next();
        ImageWriteParam param = writer.getDefaultWriteParam();
        param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
        param.setCompressionQuality(0.01f);
        //compression quality default = 100%

        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageOutputStream ios = ImageIO.createImageOutputStream(baos);
        writer.setOutput(ios);
        writer.write(null, new IIOImage(image, null, null), param);
        byte[] scaledImageInByte = baos.toByteArray()
        ios.close()
        FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath + "/" + filename.substring(0, filename.indexOf(".")) + '_passport' + filename.substring(filename.indexOf("."))), scaledImageInByte)
        render "";
    }

    def showImage(long id, String fileName, String imgType) {
        if(fileName!=null&&!"null".equals(fileName)&&fileName.length()>0) {
            response.setContentType("APPLICATION/OCTET-STREAM")
            response.setHeader("Content-Disposition", "Attachment;Filename=\"${fileName}\"")
            def file = new File("upload/" + imgType + "/" + id + "/" + fileName)
            if (file.exists()) {
                def fileInputStream = new FileInputStream(file)
                def outputStream = response.getOutputStream()
                byte[] buffer = new byte[4096];
                int len;
                while ((len = fileInputStream.read(buffer)) > 0) {
                    outputStream.write(buffer, 0, len);
                }

                outputStream.flush()
                outputStream.close()
                fileInputStream.close()
            }   else render "";
        } else render "";
    }


    def showBookPreview(int bookId, int chapterId, String pageNo) {
        response.setHeader("Content-Disposition", "inline;Filename=\"previmage-${pageNo}.jpg\"")

        def file = new File("upload/books/" + bookId + "/chapters/" + chapterId + "/images/previmage-" + pageNo + ".jpg")
        def fileInputStream = new FileInputStream(file)
        def outputStream = response.getOutputStream()
        byte[] buffer = new byte[4096];
        int len;

        while ((len = fileInputStream.read(buffer)) > 0) {
            outputStream.write(buffer, 0, len);
        }

        outputStream.flush()
        outputStream.close()
        fileInputStream.close()
    }

    def facebook() {

        if (request.getHeader('Content-Type') != null) {
            if (request.getHeader('Content-Type').startsWith("application/json")) {
                def jsonObj = request.JSON

            }
        }

    }

    def recent() {
        if (params.grade == null || ''.equals(params.grade)) redirect(action: 'index')
        if (params.resType == null || ''.equals(params.resType)) redirect(action: 'index')
    }

    def mcq() {}

    def mcqalt() {}

    def fib() {}

    def opp() {}

    def tof() {}

    def mta() {}

    def profile() {
        User user;
        if (params.resId != null) {
            ResourceDtl resourceDtl = ResourceDtl.findById(params.resId)
            user = User.findByUsername(resourceDtl.createdBy)
        } else user = User.findById(params.id)

        //please note that the new map is created to return the data named ones... other by default executeQuery returns Array
        List resources = ResourceDtl.executeQuery("select new map(t1.resType as resType,t2.topicName as topicName, t1.dateCreated as dateCreated, t2.syllabus as syllabus,t2.grade as grade,t2.subject as subject,t2.id as topicId) from ResourceDtl t1,TopicMst t2 where t1.topicId = t2.id and t1.createdBy='" + user.username + "' and t1.sharing='public' order by t1.dateCreated desc", [max: 10])
        boolean allowEdit = false;
        if (springSecurityService.currentUser != null && springSecurityService.currentUser.username == user.username) allowEdit = true;
        [user: user, resources: resources, title: user.name, allowEdit: allowEdit, noOfFriends: userManagementService.numberOfFriends(user.username), noOfGroups: userManagementService.noOfGroups(user.username)]
    }

    def footer() {}

    def mainheader() {}

    @Secured(['ROLE_USER'])
    def quizcreator() {
        if (params.topicId == null || ''.equals(params.topicId)) redirect(action: 'index')
        else {
            TopicMst topicMst = TopicMst.findById(new Integer(params.topicId))
            def keywords = topicMst.syllabus + " ,mind maps, videos , quiz, solved question paper," + topicMst.syllabus + " " + topicMst.topicName + ", " + topicMst.topicName + " " + topicMst.grade
            String topicUrl = "<a href='topic?topicId=" + ((topicMst.parentId == null) ? topicMst.id : topicMst.parentId) + "'>" + topicMst.topicName + "</a>";

            //getting the list of topics for that subject
            def sql = "select tm.topic_name,tm.id from topic_mst tm " +
                    " where tm.created_by ='" + springSecurityService.currentUser.username + "' and tm.syllabus='" + topicMst.syllabus + "' and tm.grade='" + topicMst.grade + "' and tm.syllabus_type='" + topicMst.syllabusType +
                    "' and tm.country='" + topicMst.country + "' and tm.subject='" + topicMst.subject + "'" +
                    " union " +
                    "select tm.topic_name,tm.id  from topic_mst tm  " +
                    " where tm.id in (select distinct topic_id from resource_dtl where sharing='public') and tm.syllabus='" + topicMst.syllabus + "' and tm.grade='" + topicMst.grade + "' and tm.syllabus_type='" + topicMst.syllabusType +
                    "' and tm.country='" + topicMst.country + "' and tm.subject='" + topicMst.subject + "'" +
                    " union " +
                    "select tm.topic_name,tm.id  from topic_mst tm  " +
                    " where tm.id in (select distinct topic_id from resource_dtl where created_by ='" + springSecurityService.currentUser.username + "' and (sharing is  null or sharing !='deleted')) and tm.syllabus='" + topicMst.syllabus + "' and tm.grade='" + topicMst.grade + "' and tm.syllabus_type='" + topicMst.syllabusType +
                    "' and tm.country='" + topicMst.country + "' and tm.subject='" + topicMst.subject + "'" +
                    " union " +
                    "select tm.topic_name,tm.id  from topic_mst tm" +
                    " where tm.id in (select distinct(topic_id) from resource_dtl rd , resource_group_dtl rgd, groups_dtl gd " +
                    "where rgd.resource_id=rd.id and gd.group_id=rgd.group_id and (rd.sharing is  null or rd.sharing !='deleted') and gd.username ='" + springSecurityService.currentUser.username + "') " +
                    " and tm.syllabus='" + topicMst.syllabus + "' and tm.grade='" + topicMst.grade + "' and tm.syllabus_type='" + topicMst.syllabusType +
                    "' and tm.country='" + topicMst.country + "' and tm.subject='" + topicMst.subject + "'" +
                    " order by topic_name";
            def dataSource = grailsApplication.mainContext.getBean('dataSource')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql);

            List topics = results.collect { topic ->
                return [topicName: topic[0], topicId: topic[1]]
            }



            if ("edit".equals(params.mode)) {
                String sort=""
                if (params.id == null || ''.equals(params.id)) redirect(action: 'index')
                else {
                    ResourceDtl resourceDtl = ResourceDtl.findById(new Integer(params.id))
                    ObjectiveMst objectiveMst = ObjectiveMst.findByQuizIdAndQuizSort(new Integer(resourceDtl.resLink),0)
                    if(objectiveMst != null) sort = "quizSort"
                    else sort = "id"
                    List objectives = ObjectiveMst.findAllByQuizId(new Integer(resourceDtl.resLink), [sort: sort]);
                    [topicId: (topicMst.parentId == null) ? topicMst.id : topicMst.parentId, topicMst: topicMst, resourceDtl: resourceDtl, objectives: objectives, title: topicMst.topicName, keywords: keywords, displayName: topicUrl, topics: topics]
                }

            } else
                [topicId: (topicMst.parentId == null) ? topicMst.id : topicMst.parentId, topicMst: topicMst, title: topicMst.topicName, keywords: keywords, displayName: topicUrl, topics: topics]
        }


    }


    @Secured(['ROLE_USER']) @Transactional
    def deleteQuestion() {
        ObjectiveMst objectiveMst = ObjectiveMst.findById(new Long(params.objectiveId))
        objectiveMst.quizId = new Integer("-" + params.quizId)
        objectiveMst.save(flush: true, failOnError: true)
        render "success"
        return

    }

    @Secured(['ROLE_USER'])
    def notescreator() {
        if (params.topicId == null || ''.equals(params.topicId))
            redirect(action: 'index')
        else {
            TopicMst topicMst = TopicMst.findById(new Integer(params.topicId))
            def keywords = topicMst.syllabus + " ,mind maps, videos , quiz, solved question paper," + topicMst.syllabus + " " + topicMst.topicName + ", " + topicMst.topicName + " " + topicMst.grade
            String topicUrl = "<a href='topic?topicId=" + ((topicMst.parentId == null) ? topicMst.id : topicMst.parentId) + "'>" + topicMst.topicName + "</a>";


            if ("edit".equals(params.mode)) {
                if (params.id == null || ''.equals(params.id)) redirect(action: 'index')
                else {
                    ResourceDtl resourceDtl = ResourceDtl.findById(new Integer(params.id));
                    List notes = Container.findAllById(new Integer(resourceDtl.resLink), [sort: "id"])
                    [topicId: (topicMst.parentId == null) ? topicMst.id : topicMst.parentId, topicMst: topicMst, resourceDtl: resourceDtl, notes: notes, title: topicMst.topicName, keywords: keywords, displayName: topicUrl]
                }
            } else
                [topicId: (topicMst.parentId == null) ? topicMst.id : topicMst.parentId, topicMst: topicMst, title: topicMst.topicName, keywords: keywords, displayName: topicUrl]
        }
    }

    @Secured(['ROLE_USER'])
    def addNotes() {
        def resourceDtlId, notesId;
        if ("create".equals(params.mode)) {
            Container con = new Container(textBlob: params.notes);
            con.save(failOnError: true, flush: true);
            notesId = con.id;

            def resourceDtlInstance = new ResourceDtl()
            resourceDtlInstance.resLink = notesId
            resourceDtlInstance.createdBy = springSecurityService.currentUser.username
            resourceDtlInstance.resType = params.resourceType
            resourceDtlInstance.topicId = new Integer(params.topicId)
            resourceDtlInstance.resourceName = params.resourceName
            resourceDtlInstance.save(failOnError: true, flush: true)
            if(resourceDtlInstance.sharing==null){
                dataProviderService.resourceCacheUpdate(resourceDtlInstance.id)
                ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtlInstance.chapterId)
                dataNotificationService.resourceUpdated(chaptersMst.id, chaptersMst.bookId)

            }else{
                dataProviderService.getChapterResourcesForUser(new Long(params.chapterId),springSecurityService.currentUser.username)
            }
            userManagementService.addPoints(springSecurityService.currentUser.username, "CP", "ADDNOTES", resourceDtlInstance.id.toString());

            resourceDtlId = resourceDtlInstance.id
        } else if ("edit".equals(params.mode)) {
            Container con = Container.findById(new Integer(params.notesId));
            con.textBlob = params.notes;
            con.save(failOnError: true, flush: true);

            ResourceDtl resourceDtl = ResourceDtl.findById(new Integer(params.resourceDtlId))
            resourceDtl.resourceName = params.resourceName
            resourceDtl.save(failOnError: true, flush: true)

            resourceDtlId = params.resourceDtlId
            notesId = params.notesId
        }

        final MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        MultipartFile file = multiRequest.getFile("file");
//        def file = request.getFile('file')

        if (file != null && !file.empty) {
            def filename = file.originalFilename
            def resLink = "upload/notes/" + objectiveMstId + "/" + filename
            File uploadDir = new File("upload/notes/" + objectiveMstId)
            if (!uploadDir.exists()) uploadDir.mkdirs()
            file.transferTo(new File(grailsApplication.config.grails.basedir.path+resLink))

            String sql = "update ObjectiveMst set questionFilename='" + filename + "', questionFilepath='" + resLink + "' where id=" + objectiveMstId;
            ObjectiveMst.executeUpdate(sql);
        }



        if (params.topicId == null || ''.equals(params.topicId))
            redirect(action: 'index')
        else {
            TopicMst topicMst = TopicMst.findById(new Integer(params.topicId))
            User user = User.findByUsername(topicMst.createdBy)
            redirect(controller: 'funlearn', action: 'topic', params: [topicId: (topicMst.parentId == null) ? topicMst.id : topicMst.parentId, topicMst: topicMst, user: user])
        }
    }

    def getNotes() {
        ResourceDtl resourceDtl = ResourceDtl.findById(new Integer(params.notesId))
        if (canSeeResource(resourceDtl)) {

            String sql = "select rd.resourceName, con.textBlob, rd.id from ResourceDtl rd,Container con" +
                    " where rd.id=" + params.notesId + " and con.id=rd.resLink"

            List results = ResourceDtl.executeQuery(sql)

            List noteList = results.collect { note ->
                return [resourceName: note[0], notes: note[1], id: note[2]]
            }

            userManagementService.addPoints((springSecurityService.currentUser != null ? springSecurityService.currentUser.username : ""), "LP", "VIEW", params.notesId);

            def json =
                    [
                            'results': noteList,
                            'status' : noteList ? "OK" : "Nothing present"
                    ]
            render json as JSON
        }
    }

    def notesToPDF() {

        ResourceDtl resourceDtl = ResourceDtl.findById(new Integer(params.notesId))
        if (canSeeResource(resourceDtl)) {
            String sql = "select rd.resourceName, con.textBlob, rd.id from ResourceDtl rd,Container con" +
                    " where rd.id=" + params.notesId + " and con.id=rd.resLink"

            List results = ResourceDtl.executeQuery(sql)

            List notesList = results.collect { note ->
                return [resourceName: note[0], notes: note[1], id: note[2]]
            }

            def htmlText = notesList[0].notes;
            def baos = new ByteArrayOutputStream();
            def renderer = new ITextRenderer((55.0 * 4.0 / 3.0), 40);

            htmlText = htmlText.replace("<br>", "<br/>");
            htmlText = htmlText.replace("</br>", "<br/>");
            htmlText = htmlText.replace("<BR>", "<br/>");
            htmlText = htmlText.replace("</BR>", "<br/>");

            def tidy = new Tidy();
            tidy.setShowErrors(0);
            tidy.setQuiet(true);
            tidy.setShowWarnings(false)
            tidy.setXHTML(true);
            tidy.setNumEntities(true);

            org.w3c.dom.Document doc = tidy.parseDOM(new java.io.ByteArrayInputStream(htmlText.getBytes()), null);
            tidy.pprint(doc, baos);
            htmlText = baos.toString();
            baos.close();

            baos = new ByteArrayOutputStream();
            renderer.setDocumentFromString(htmlText);
            renderer.layout();
            renderer.createPDF(baos);

            response.setContentType("application/octet-stream")
            response.setHeader('Content-disposition', 'Attachment; filename=\"' + notesList[0].resourceName + '.pdf\"')
            response.outputStream << baos.toByteArray()
            response.outputStream.flush()
            response.outputStream.close()
            baos.close()
        }
    }
    @Transactional
    def careers() {
        def jobs = Jobs.list()
        [jobs: jobs, title: "Career"]
    }

    def aboutus() {
        [title: "About Us"]
    }

    def privacy() {
        [
                title: "Privacy",
                newCss: true
        ]
    }

    def success() {
        session.setAttribute("syllabusType", "success");
        session.setAttribute("country", "India");
        if (springSecurityService.currentUser != null) redirect([uri: '/funlearn/home'])
    }

    def loginFailed() {
        redirect(controller: "funlearn", params: [loginFailed: true])
    }

    @Secured(['ROLE_USER']) @Transactional
    def home() {
        if (session.getAttribute("userdetails") == null) {
            session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)
        }

        if (session.getAttribute("userdetails") != null) {
            def list = PointsDtl.executeQuery("select type, sum(points) from PointsDtl where username='" + springSecurityService.currentUser.username + "' group by username,type")

            session["LP"] = 0;
            session["SP"] = 0;
            session["CP"] = 0;

            list.each { item ->
                switch (item[0]) {
                    case "LP":
                        session["LP"] = item[1];
                        break;
                    case "SP":
                        session["SP"] = item[1];
                        break;
                    case "CP":
                        session["CP"] = item[1];
                }
            }
        }

        if (session.getAttribute("syllabusType") == null) {
            session.setAttribute("syllabusType", "" + session["userdetails"].registeredFrom);
            session.setAttribute("country", "India");
        }
        /** if ("true".equals(session.getAttribute("harpercollins")))
         redirect(controller: "harpercollins", action: "main");
         if ("true".equals(session.getAttribute("wonderpublish")))
         redirect(controller: "books", action: "books");
         if ("true".equals(session.getAttribute("vtu")))
         redirect(controller: "books", action: "vtulibrary");*/

        if (session.getAttribute("userdetails").sex == null || "".equals(session.getAttribute("userdetails").sex))
            redirect(controller: "creation", action: "welcomeModal")


        [title: "Wonderslate", keywords: "Create your own study material, CBSE, ICSE, State boards", homeDiscover: "true"]
    }

    def navheader() {}

    def pnavheader() {}

    @Secured(['ROLE_USER'])
    def findFriends() {
        [title: "Find Friends"]
    }


    @Secured(['ROLE_USER'])
    def findFriendsJSON() {
        String sql = "select um.name, um.profilepic, f.status, um.id, f.friend1, f.friend2 \n" +
                "from user um left   join friends f  \n" +
                "on  ((um.username = f.friend1 and f.friend2='" + springSecurityService.currentUser.username + "')\n" +
                " or (um.username = f.friend2 and f.friend1='" + springSecurityService.currentUser.username + "'))\n" +
                "  where (um.win='" + params.friend + "' or lower(um.name) like lower('%" + params.friend + "%')) \n" +
                "  and um.username!='" + springSecurityService.currentUser.username + "' ";

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        //List results = User.executeQuery(sql)
        List friends = results.collect { friend ->
            return [name: friend[0], profilepic: friend[1], status: friend[2], friendId: friend[3], friend1currentuser: (springSecurityService.currentUser.username.equals(friend[4])) ? "true" : "false"]
        }

        def json =
                [
                        'results': friends,
                        'status' : friends ? "OK" : "Nothing present"
                ]
        render json as JSON
    }

    def numberOfFriends() {
        String sql = "select um.name\n" +
                "from user um left   join friends f  \n" +
                "on  ((um.username = f.friend1 and f.friend2='" + springSecurityService.currentUser.username + "')\n" +
                " or (um.username = f.friend2 and f.friend1='" + springSecurityService.currentUser.username + "'))\n" +
                "  where (um.win='" + params.friend + "' or lower(um.name) like lower('%" + params.friend + "%')) \n" +
                "  and um.username!='" + springSecurityService.currentUser.username + "' ";

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        return results.size();
    }

    def tour() {
        [title: "Tour"]
    }

    def groups() {
        [title: "Groups"]
    }

    @Secured(['ROLE_USER'])
    def groupdtl() {
        if (userManagementService.canSeeGroup(params.groupId)) {
            Groups group = Groups.findById(new Integer(params.groupId))
            [group: group, addMember: userManagementService.isGroupAdmin(params.groupId)]
        } else redirect(controller: "funlearn", action: "home")

    }

    def tandc() {

    }

    def termsandconditions() {
        [
                title: "Terms & Conditions",
                newCss : true
        ]
    }

    def plindex() {}

    def wsindex() {}

    def plfooter() {}

    @Secured(['ROLE_USER']) @Transactional
    def updateWithQuizAnswers() {
        boolean testSeries=false;
        def resId
        def rank=null
        def source="web"
        if (springSecurityService.currentUser != null) {
            boolean jsonData = false;
            Quizrecorder quizrecorder
            def bookId = null;
            def score = null;
            if (request.getHeader('Content-Type') != null && request.getHeader('Content-Type').startsWith("application/json")) jsonData = true;
            if (jsonData) {
                def jsonObject = request.JSON
                source="mobile"
                DateFormat df = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
                Date quizDate = df.parse(jsonObject.takenAt);
                if(jsonObject.quizId!=null){
                    ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(jsonObject.quizId))
                    if(resourceDtl!=null&&resourceDtl.testStartDate!=null) {
                        testSeries=true
                        resId = resourceDtl.id
                    }
                    if(resourceDtl!=null&&resourceDtl.chapterId!=null){
                        BooksMst booksMst = dataProviderService.getBooksMst(dataProviderService.getChaptersMst(resourceDtl.chapterId).bookId)
                        if("test".equals(booksMst.bookType)) bookId = booksMst.id
                    }
                }
                asyncLogsService.addQuizRecorderJSON(springSecurityService.currentUser.username,jsonObject,bookId)
            } else {
                if(params.quizid!=null){
                    ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(params.quizid))
                    if(resourceDtl!=null&&resourceDtl.testStartDate!=null) {
                        testSeries=true
                        resId = resourceDtl.id
                    }
                    if(resourceDtl!=null&&resourceDtl.chapterId!=null){
                        BooksMst booksMst = dataProviderService.getBooksMst(dataProviderService.getChaptersMst(resourceDtl.chapterId).bookId)
                        if("test".equals(booksMst.bookType)) {
                            bookId = booksMst.id
                        }
                    }
                }
                asyncLogsService.addQuizRecorder(springSecurityService.currentUser.username,params,bookId)
            }
            ResourceDtl resourceDtl = dataProviderService.getResourceDtl(resId)
            if(testSeries){
                List testsTakenList = Quizrecorder.findAllByUsernameAndQuizid(springSecurityService.currentUser.username,resId)
                if(testsTakenList.size()==1){
                    //test taken for the first time so do the calculation
                    if(resourceDtl.testResultDate==null) {
                        utilService.calculateRank(resId)
                        dataProviderService.getTestRanks(""+resId)
                    }
                    String logEnabled = dataProviderService.isResourceLoggingEnabled()
                    if("true".equals(logEnabled)) {

                        ResourceView resourceView = new ResourceView(resourceDtlId: resId,
                                username: springSecurityService.currentUser.username, action: "view", source: source);
                        resourceView.save(failOnError: true, flush: true)
                    }

                    if(resourceDtl!=null && resourceDtl.chapterId!=null){
                        if(redisService.("seenResources_"+springSecurityService.currentUser.username+"_"+resourceDtl.chapterId)==null){
                            dataProviderService.updateUsageList(resourceDtl.chapterId)
                        } else {
                            if((""+redisService.("seenResources_"+springSecurityService.currentUser.username+"_"+resourceDtl.chapterId)).indexOf(","+resId+",")==-1)
                                redisService.("seenResources_"+springSecurityService.currentUser.username+"_"+resourceDtl.chapterId) = redisService.("seenResources_"+springSecurityService.currentUser.username+"_"+resourceDtl.chapterId)+resId+","
                        }
                    }
                }
            }
        }
        def json =
                [
                        'status': "OK",
                        'rank': rank
                ]
        render json as JSON

    }
    @Transactional
    def  updatedorisResourceView() {
        ResourceView resourceView = new ResourceView(resourceDtlId: params.resId,
                username: springSecurityService.currentUser.username,siteId:9, action: "view", source: "web");
        resourceView.save(failOnError: true, flush: true)

    }

    @Transactional
    def  updateDorisInstructorResourceView() {
        Integer siteId = getSiteId(request)
        InstructorResourceView instructorResourceView = new InstructorResourceView(instructorResId: params.resId,
                username: springSecurityService.currentUser.username,siteId:siteId,bookId: new Long(params.bookId));
        instructorResourceView.save(failOnError: true, flush: true)

    }

    @Secured(['ROLE_USER']) @Transactional
    def keyValueRecorder(){
        if (springSecurityService.currentUser != null) {
            boolean jsonData = false;
            if (request.getHeader('Content-Type') != null && request.getHeader('Content-Type').startsWith("application/json")) jsonData = true;
            if (jsonData) {

                def jsonObject = request.JSON

                JSONArray arrayObject = jsonObject.queData;
                org.grails.web.json.JSONObject questionDetails

                for (int i = 0; i < (arrayObject.length()); i++) {
                    questionDetails = arrayObject.get(i);
                    if("skipped".equals(questionDetails.correctAnswer)) continue;
                    KeyValueRecorder keyValueRecorder = new KeyValueRecorder(keyValueId:new Long(questionDetails.id),username:springSecurityService.currentUser.username,
                            correct:questionDetails.correctAnswer,quizType: jsonObject.quizType,resId: jsonObject.resId)
                    keyValueRecorder.save(failOnError: true, flush: true)

                }

            } else {
                KeyValues temp = KeyValues.findById(new Long(params["id0"]))

                for (int i = 0; i < (Integer.parseInt(params.noOfQuestions)); i++) {
                    if("skipped".equals(params["correctAnswer" + i])) continue;
                    KeyValueRecorder keyValueRecorder = new KeyValueRecorder(keyValueId:new Long(params["id" + i]),username:springSecurityService.currentUser.username,
                            correct:params["correctAnswer" + i],quizType: params.quizType,resId: temp.resId)
                    keyValueRecorder.save(failOnError: true, flush: true)
                }
            }
        }
        def json =
                [
                        'status': "OK"
                ]
        render json as JSON

    }

    @Secured(['ROLE_USER'])
    def mylibrary() {
        if (springSecurityService.currentUser != null && session.getAttribute("userdetails") == null) {
            session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)
        }

        if (!((params.topicId == null || ''.equals(params.topicId)) && (params.id == null || ''.equals(params.id)))) {
            def topicId
            def resType
            def link
            if (params.id != null && !''.equals(params.topicId)) {
                ResourceDtl rd = ResourceDtl.findById(new Integer(params.id))
                topicId = rd.topicId
                resType = rd.resType
                link = rd.resLink

            }

            TopicMst topicMst = TopicMst.findById(new Integer(params.topicId == null || ''.equals(params.topicId) ? topicId : params.topicId))
            Cookie cookie = new Cookie("syllabus", topicMst.syllabus)
            cookie.maxAge = 99999999
            cookie.setPath("/");
            response.addCookie(cookie)
            cookie = new Cookie("grade", "" + topicMst.grade)
            cookie.setPath("/");
            cookie.maxAge = 99999999
            response.addCookie(cookie)
            cookie = new Cookie("subject", topicMst.subject)
            cookie.setPath("/");
            cookie.maxAge = 99999999
            cookie = new Cookie("level", topicMst.syllabusType)
            cookie.setPath("/");
            cookie.maxAge = 99999999
            response.addCookie(cookie)

            def keywords = topicMst.syllabus + " ,mind maps, videos , quiz, solved question paper," + topicMst.syllabus + " " + topicMst.topicName + ", " + topicMst.topicName + " " + topicMst.grade

            [topicId  : (topicMst.parentId == null) ? topicMst.id : topicMst.parentId,
             topicMst : topicMst, displayName: "My Library",
             giveAdd  : "true", title: topicMst.topicName, keywords: keywords,
             id       : params.id,
             resType  : resType,
             link     : link,
             topicName: topicMst.topicName,
             title    : "My Library", keywords: "Create your own study material, CBSE, ICSE, State boards"
            ]
        } else {
            [displayName: "My Library", topicName: "", title: "My Library", keywords: "Create your own study material, CBSE, ICSE, State boards"]
        }
    }

    def topicinclude() {
        println("topic include 1")
    }

    def topicdisplay() {}

    def topicscripts() {}

    @Transactional
    def getSyllabusGradeSubject() {
        String syllabusType = "school"
        String country = "India"

        if (params.syllabusType != null && !"".equals(params.syllabusType)) syllabusType = params.syllabusType
        if (params.country != null && !"".equals(params.country)) country = params.country

        def sql = "select sb.syllabus,sb.grade,sb.name from subject_mst sb  " +
                " where sb.country='" + country + "' and sb.syllabus_type='" + syllabusType + "'" +
                " order by sb.syllabus,sb.grade,sb.name"

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);

        List subjects = results.collect { subject ->
            return [syllabus: subject[0], grade: subject[1], subject: subject[2]]
        }
        if ("School".equals(syllabusType)) {
            def gradesMst = GradeMst.findAllBySyllabusType(syllabusType);
            def grades = gradesMst.collect { grade ->
                return [grade: grade.grade]
            }
            def boardsMst = SyllabusMst.findAllBySyllabusType(syllabusType)
            def boards = boardsMst.collect { board ->
                return [board: board.name]
            }
            def json =
                    ['syllabusType': syllabusType,
                     'subjects'    : subjects,
                     'grades'      : grades,
                     'boards'      : boards,
                     'status'      : subjects ? "OK" : "Nothing present"
                    ]
            render json as JSON

        } else {
            def json =
                    ['syllabusType': syllabusType,
                     'results'     : subjects,
                     'status'      : subjects ? "OK" : "Nothing present"
                    ]
            render json as JSON
        }
    }

    @Transactional
    boolean   canSeeResource(ResourceDtl resourceDtl) {
        boolean showDebug=false
        Integer siteId = getSiteId(request)
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        if("true".equals(siteMst.mainResourcesPage) && resourceDtl != null){
            return  userManagementService.canSeeResourceCheck(resourceDtl,session,request,response)
        }else {
            if (resourceDtl != null) {
                if ((siteId == 25 || siteId == 12 || siteId == 23) && utilService.hasLibraryAccess(request, siteId)) return true
                if (siteId == 21 && ("currentaffairs".equals(resourceDtl.quizMode) || "jobalerts".equals(resourceDtl.quizMode))) return true
                if ("public".equals(resourceDtl.privacyLevel)) {
                    if (showDebug) println(" public for resourceId=" + resourceDtl.id)
                    return true
                } else if (springSecurityService.currentUser != null && resourceDtl.createdBy.equals(springSecurityService.currentUser.username)) {
                    return true
                } else { // wonderslate check
                    if (showDebug) println(" Wonderslate check=" + resourceDtl.id)
                    User user = null
                    if (springSecurityService.currentUser != null) {
                        user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
                    } else if (siteId == 12 || siteId == 23 || siteId == 25) {
                        if (utilService.hasLibraryAccess(request, siteId)) {
                            return true
                        } else {
                            return false
                        }
                    }
                    if (user != null && user.authorities.any {
                        it.authority == "ROLE_WS_CONTENT_ADMIN"
                    } && user.publisherId == null) {
                        return true
                    } else if (resourceDtl.topicId != null && !"".equals("" + resourceDtl.topicId)) {
                        return (springSecurityService.currentUser != null &&
                                userManagementService.canSeeResource(springSecurityService.currentUser.username, resourceDtl.createdBy, resourceDtl.id, resourceDtl.sharing))
                    } else if (resourceDtl.chapterId != null && !"".equals("" + resourceDtl.chapterId)) {
                        //wonderpublish check
                        if (showDebug) println(" wonderpublish for resourceId=" + resourceDtl.id)
                        ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)
                        BooksMst booksMst = dataProviderService.getBooksMst(chaptersMst.bookId)

                        if ("true".equals("" + chaptersMst.previewChapter) || ("published".equals(booksMst.status) && (booksMst.price == null || booksMst.price.doubleValue() == 0))) {
                            if (showDebug) println(" preview chapter for resourceId=" + resourceDtl.id)
                            return true
                        } else {
                            if (springSecurityService.currentUser != null) {
                                boolean hasBookAccess = false
                                List booksList
                                if (siteId == 1 || siteId == 3 || siteId==28 || siteId==37 || siteId==38 || siteId==39) {
                                    if (wsLibraryService.bookAccessForUser(booksMst.id + "", request, session)) {
                                        hasBookAccess = true;
                                    }
                                }else {
                                    if (redisService.(springSecurityService.currentUser.username + "_" + "booksList") == null) {
                                        dataProviderService.getBooksListForUser()
                                        booksList = new JsonSlurper().parseText(redisService.(springSecurityService.currentUser.username + "_" + "booksList"))
                                        for(int i=0;i<booksList.size();i++) {
                                            def book = booksList[i]
                                            if (Long.parseLong("" + book.id) == chaptersMst.bookId.longValue() && "book".equals(book.permissionType)) {
                                                hasBookAccess = true
                                            }else {
                                                //now check if the book is created from master book
                                                BooksDtl booksDtl = BooksDtl.findByBookId(new Long("" + book.id))
                                                if (booksDtl != null) {
                                                    if (chaptersMst.bookId.longValue() == booksDtl.masterBookId.longValue()) {
                                                        hasBookAccess = true
                                                    }
                                                }
                                            }
                                            if(hasBookAccess) break;
                                        }
                                    }
                                }
                                if (hasBookAccess) {
                                    if (showDebug) println(" booksPermission for resourceId=" + resourceDtl.id)
                                    return true
                                } else {
                                    //check for publishing thingy
                                    if (showDebug) println(" wonderpublish check  not bought this chapter")
                                    if (("" + user.publisherId).equals("" + booksMst.publisherId) || user.authorities.any {
                                        it.authority == "ROLE_WS_CONTENT_CREATOR"
                                    }) {
                                        if (showDebug) println(" checking publisher for resourceId=" + resourceDtl.id + " username=" + springSecurityService.currentUser.username);
                                        return true
                                    } else {
                                        if (showDebug) println("entered the last step and session siteId=" + session["siteId"]);

                                        if (session["siteId"] != null && session["siteId"].intValue() == 9 && session["siteId"].intValue() == booksMst.siteId.intValue()) {
                                            if (showDebug) println "its sage site and book"
                                            return true
                                        }

                                        return false
                                    }
                                }
                            } else return false
                        }
                    } else return false
                }
            } else {
                return false
            }
        }
    }

    def addQuizPoints() {
        if (springSecurityService.currentUser != null)
            userManagementService.addUpdatePoints(springSecurityService.currentUser.username, "LP", "PLAY", params.quizId, new Integer(params.correctAnswers))
    }

    def getBoardsForSyllabusType() {
        def boardsMst = SyllabusMst.findAllBySyllabusType(params.syllabusType)

        def boards = boardsMst.collect { board ->
            return [board: board.name]
        }
        def json =
                ['boards': boards,
                 'status': boards ? "OK" : "Nothing present"
                ]
        render json as JSON
    }

    def getMP3(long id) {

        ResourceDtl documentInstance = ResourceDtl.get(id)
        if (documentInstance == null) {
            flash.message = "Document not found."
            redirect(action: 'list')
        } else {
            if (canSeeResource(documentInstance)) {
                def file = new File(documentInstance.resLink)
                response.setContentType("APPLICATION/OCTET-STREAM")
                response.setHeader("Content-Disposition", "inline;Filename=\"${documentInstance.filename}\"")
                response.setHeader("Content-Length", "${file.length()}")


                def fileInputStream = new FileInputStream(file)
                def outputStream = response.getOutputStream()
                byte[] buffer = new byte[4096];
                int len;
                while ((len = fileInputStream.read(buffer)) > 0) {
                    outputStream.write(buffer, 0, len);
                }

            }
        }
    }

    def getMedia() {
        ResourceDtl documentInstance = ResourceDtl.findById(new Integer(params.id))
        if (documentInstance == null) {
            flash.message = "Document not found."
            redirect(action: 'list')
        } else {
            if (canSeeResource(documentInstance)) {
                def file = new File(documentInstance.resLink)
                response.setContentType("APPLICATION/OCTET-STREAM")
                response.setHeader("Content-Disposition", "inline;Filename=\"${documentInstance.filename}\"")
                response.setHeader("Content-Length", "${file.length()}")
                def fileInputStream = new FileInputStream(file)
                def outputStream = response.getOutputStream()
                byte[] buffer = new byte[4096];
                int len;
                while ((len = fileInputStream.read(buffer)) > 0) {
                    outputStream.write(buffer, 0, len);
                }

            }
        }
    }
    @Transactional
    def getFlashCards(){
        if(params.dailyTestId!=null){
            String inputDate = params.dateInput
            String dailyTestId = params.dailyTestId
            DailyTestsMst dailyTestsMst = DailyTestsMst.findById(new Integer(dailyTestId))
            if(redisService.("dailyTests_"+inputDate+"_"+dailyTestId)==null) prepjoyService.getDailyTests(inputDate,dailyTestId)
            List mcqs = new JsonSlurper().parseText(redisService.("dailyTests_"+inputDate+"_"+dailyTestId))
            List keyValues = mcqs.collect { mcq ->
                String answer = ""
                if ("Yes".equals(mcq.ans1)) answer = mcq.op1
                else if ("Yes".equals(mcq.ans2)) answer = mcq.op2
                else if ("Yes".equals(mcq.ans3)) answer = mcq.op3
                else if ("Yes".equals(mcq.ans4)) answer = mcq.op4
                else if ("Yes".equals(mcq.ans5)) answer = mcq.op5

                return [id: mcq.id, term: mcq.ps, definition: answer]
            }
            def json = ["keyValues"      : keyValues, "resId": params.resId, "resType": "Multiple Choice Questions", "resourceName": dailyTestsMst.testName,
                        "canEdit"        :  "false", description: "",
                        dateCreated      : inputDate, fastestTime: null,
                        createdBy        :  "",
                        createdByUsername: "",
                        profilePic       : ""]
            render json as JSON

        }
        else if(params.resId!=null&&!"null".equals(params.resId)&&!"".equals(params.resId)&&!"undefined".equals(params.resId)) {
            ResourceDtl documentInstance = ResourceDtl.get(new Long(params.resId))
            if (canSeeResource(documentInstance)) {
                if ("KeyValues".equals(documentInstance.resType)) {
                    List keyValues = KeyValues.findAllByResIdAndStatus(new Long(params.resId), "active");
                    if(params.noOfQuestions){
                        keyValues = keyValues.take(Integer.parseInt(params.noOfQuestions))
                    }

                    def json = ["keyValues"      : keyValues, "resId": params.resId, "resType": documentInstance.resType, "resourceName": documentInstance.resourceName,
                                "canEdit"        : (springSecurityService.currentUser != null && springSecurityService.currentUser.username.equals(documentInstance.createdBy)) ? "true" : "false",
                                privacyLevel     : documentInstance.privacyLevel, description: documentInstance.description, dateCreated: documentInstance.dateCreated, fastestTime: documentInstance.flashCardFastTime,
                                createdBy        : documentInstance.sharing != null ? dataProviderService.getUserMst(documentInstance.createdBy).name : "",
                                createdByUsername: documentInstance.sharing != null ? documentInstance.createdBy : "",
                                profilePic       : documentInstance.sharing != null ? dataProviderService.getUserMst(documentInstance.createdBy).profilepic : ""
                    ]
                    render json as JSON
                } else {
                    List mcqs = ObjectiveMst.findAllByQuizId(new Integer(documentInstance.resLink))
                    List keyValues = mcqs.collect { mcq ->
                        String answer = ""
                        if ("Yes".equals(mcq.answer1)) answer = mcq.option1
                        else if ("Yes".equals(mcq.answer2)) answer = mcq.option2
                        else if ("Yes".equals(mcq.answer3)) answer = mcq.option3
                        else if ("Yes".equals(mcq.answer4)) answer = mcq.option4
                        else if ("Yes".equals(mcq.answer5)) answer = mcq.option5

                        return [id: mcq.id, term: mcq.question, definition: answer]
                    }
                    def json = ["keyValues"      : keyValues, "resId": params.resId, "resType": documentInstance.resType, "resourceName": documentInstance.resourceName,
                                "canEdit"        : (springSecurityService.currentUser != null && springSecurityService.currentUser.username.equals(documentInstance.createdBy)) ? "true" : "false", description: documentInstance.description,
                                dateCreated      : documentInstance.dateCreated, fastestTime: documentInstance.flashCardFastTime,
                                createdBy        : documentInstance.sharing != null ? dataProviderService.getUserMst(documentInstance.createdBy).name : "",
                                createdByUsername: documentInstance.sharing != null ? documentInstance.createdBy : "",
                                profilePic       : documentInstance.sharing != null ? dataProviderService.getUserMst(documentInstance.createdBy).profilepic : ""]
                    render json as JSON
                }
            } else {
                def json = ["keyValues": null]
                render json as JSON
            }
        }else {
            def json = ["keyValues": null]
            render json as JSON
        }
    }



    @Transactional
    def getUserAnalyticsForStudySets(){
        String sql = "SELECT count(correct),correct,key_value_id,username FROM wslog.key_value_recorder \n" +
                " where username='"+springSecurityService.currentUser.username+"' and res_id="+params.resId+" group by correct,key_value_id,username order by key_value_id";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);

        List keyValueRecordings = results.collect { result ->
            return [correct: result[1], keyValueId: result[2],count: result[0]]
        }

        def json = ["keyValueRecordings":keyValueRecordings]
        render json as JSON
    }


    def getHtmls() {
        ResourceDtl documentInstance = dataProviderService.getResourceDtl(new Long(params.resId))
        boolean ebook=false
        if(documentInstance.filename!=null&&(documentInstance.filename.indexOf(".pdf")!=-1||documentInstance.filename.indexOf(".zip")!=-1)) ebook=true
        if (documentInstance != null && canSeeResource(documentInstance)) {
            def filename = documentInstance.resLink.substring(0, (documentInstance.resLink.lastIndexOf(".")==-1?documentInstance.resLink.length():documentInstance.resLink.lastIndexOf(".")))+".ws"
            def isZipBook = documentInstance.filename.toLowerCase().endsWith(".zip") || documentInstance.filename.toLowerCase().endsWith(".pdf")
            def htmlsContent = new String(Files.readAllBytes(Paths.get(filename)), StandardCharsets.UTF_8)


            def json = [
                    'htmlsContent' : htmlsContent,
                    'status' : htmlsContent.length()>0 ? "OK" : "Nothing present",
                    'eBook' : ebook,
                    'isZipBook' : isZipBook
            ]

            render json as JSON
        }
    }

    @Transactional
    def getHtmlsForWeb() {
        ResourceDtl documentInstance = dataProviderService.getResourceDtl(new Long(params.resId))
        if ((documentInstance != null && canSeeResource(documentInstance)) || (params.mode == 'edit')) {
            def filename = documentInstance.resLink.substring(0, (documentInstance.resLink.lastIndexOf(".")==-1?documentInstance.resLink.length():documentInstance.resLink.lastIndexOf(".")))+".ws"
            def isZipBook = documentInstance.filename.toLowerCase().endsWith(".zip") || documentInstance.filename.toLowerCase().endsWith(".pdf")

            def file = new File(filename)
            def isOnlineHtml = !file.exists()
            def htmlsContent = new String(Files.readAllBytes(Paths.get(filename)), StandardCharsets.UTF_8)
            def cssString = ""
            def extraPath = "/OEBPS"


            def replaceStr = "/funlearn/downloadEpubImage"+
                    "?source="+documentInstance.resLink.substring(0,documentInstance.resLink.lastIndexOf('/')+ 1)+"extract"+
                    (isZipBook?"":extraPath)+"/"+filename.substring(0,filename.lastIndexOf("/") +1);

            if (springSecurityService.currentUser != null) {
                asyncLogsService.updateChapterAccess(springSecurityService.currentUser.username,documentInstance.id,documentInstance.chapterId)
            }

            def json = [
                    'htmlsContent': htmlsContent,
                    'cssString'   : cssString,
                    'onlineHtml'  : isOnlineHtml,
                    'resourceName': documentInstance.resourceName,
                    'resId'       : params.resId,
                    'status'      : htmlsContent.length()>0 ? "OK" : "Nothing present",
                    'isZipBook'   : isZipBook,
                    'replaceStr'  : replaceStr
            ]

            render json as JSON
        }
    }

    def downloadEpubImage() {
        if(params.source!=null){
            String src = params.source
            File file = new File(src)

            if(!file.exists() && src.toLowerCase().endsWith(".png")) {
                src = params.source.replace(".png", ".jpg")
                file = new File(src)
            }

            if (!file.exists()) {
                src = params.source.replace("/OEBPS", "")
                file = new File(src)
            }

            if (!file.exists() && src.endsWith(".jpg") ) {
                src = params.source.replace(".jpg", ".png")
                file = new File(src)
            }

            if(file.exists()) {
                if(file.getName().toLowerCase().endsWith(".svg")) {
                    String fileStr = FileUtils.readFileToString(file,"UTF-8")

                    if(fileStr.indexOf("xlink:href=\"/")==-1) {
                        fileStr = fileStr.replaceAll("xlink:href=\"","xlink:href=\""+"/funlearn/downloadEpubImage"+
                                "?source="+src.substring(0,src.lastIndexOf("/")+1)).replaceAll(".png",".jpg")
                        FileUtils.writeStringToFile(file,fileStr,"UTF-8")
                    }

                    response.setContentType("image/svg+xml")
                } else if(file.getName().toLowerCase().endsWith(".css")) {
                    String fileStr = FileUtils.readFileToString(file,"UTF-8")

                    if(fileStr.indexOf("url(\"/")==-1) {
                        fileStr = fileStr.replace("url(\"","url(\""+"/funlearn/downloadEpubImage"+
                                "?source="+src.substring(0,src.lastIndexOf("/")+1))
                        FileUtils.writeStringToFile(file,fileStr,"UTF-8")
                    }

                    response.setContentType("text/css")
                } else {
                    response.setContentType("APPLICATION/OCTET-STREAM")
                }

                response.setHeader("Content-Disposition", "inline;Filename=\"${file.getName()}\"")
                response.setHeader("Content-Length", "${file.length()}")

                def fileInputStream = new FileInputStream(file)
                def outputStream = response.getOutputStream()
                byte[] buffer = new byte[4096]
                int len
                while ((len = fileInputStream.read(buffer)) > 0) {
                    outputStream.write(buffer, 0, len)
                }

                outputStream.flush()
                outputStream.close()
                fileInputStream.close()
            } else render ""
        }else{
            render ""
        }
    }

    def downloadUpdatedFile(long id) {
        ResourceDtl documentInstance = dataProviderService.getResourceDtl(id)
        if (documentInstance != null && canSeeResource(documentInstance) && documentInstance.filename!=null) {
            def fileNm = documentInstance.filename
            boolean fileExist = false
            def file = null
            if(documentInstance.modifiedFile!=null) {
                file = new File(grailsApplication.config.grails.basedir.path + documentInstance.resLink.replace(fileNm, documentInstance.modifiedFile))

                if (fileNm.toLowerCase().endsWith(".pdf")) {
                    fileNm = fileNm.substring(0, fileNm.lastIndexOf(".")) + ".zip"
                }

                if (!file.exists()) {
                    file = new File(grailsApplication.config.grails.basedir.path + documentInstance.resLink.replace(fileNm, documentInstance.modifiedFile));
                }
                if (file.exists()) fileExist = true
            }


            if (!fileExist) {
                def  filename = documentInstance.resLink.substring(0,documentInstance.resLink.lastIndexOf('/'))+"/"+documentInstance.filename+".ws"
                file  = new File(filename)
            }

            //response.setContentType("APPLICATION/OCTET-STREAM")
            response.setHeader("Content-Disposition", "Attachment;Filename=\"${fileNm}\"")
            response.setHeader("Content-Length", "${file.length()}")
            def fileInputStream = new FileInputStream(file)
            def outputStream = response.getOutputStream()
            byte[] buffer = new byte[4096];
            int len;

            while ((len = fileInputStream.read(buffer)) > 0) {
                outputStream.write(buffer, 0, len);
            }

            outputStream.flush()
            outputStream.close()
            fileInputStream.close()
        }
    }

    boolean isFunctionalityPresentForThisSite(String functionality,Integer siteId){

        if(servletContext.getAttribute(functionality+siteId)==null){
            FunctionalityMst functionalityMst = FunctionalityMst.findByFunctionalityAndSiteId(functionality,siteId)
            if(functionalityMst!=null) servletContext.setAttribute((functionality+siteId),"true")
            else servletContext.setAttribute((functionality+siteId),"false")
        }
        if("true".equals(servletContext.getAttribute(functionality+siteId))) return true
        else return false

    }

    def Integer getSiteId(request){
        Integer siteId = new Integer(1);
        if(session["siteId"]!=null){
            siteId = (Integer)session["siteId"]
        } else {
            def jsonObj = request.JSON
            if(jsonObj.siteId!=null) siteId = new Integer(jsonObj.siteId);
            else if(params.siteId!=null) siteId = new Integer(params.siteId);
        }

        return siteId;
    }

    def getVideoPolls(){
        List videoPolls = VideoPollMst.findAllByResId(new Long(params.resId))
        def json = ["videoPolls":videoPolls]
        render json as JSON
    }
    @Secured(['ROLE_USER'])
    def updateChapterAccess(){
        userManagementService.updateChapterAccess(new Long(params.bookId),new Long(params.chapterId),new Long(params.resourceId))
        def json = ["status":"OK"]
        render json as JSON
    }
    @Secured(['ROLE_USER'])
    def getLastReadBooks(){
        List lastReadBooks  = userManagementService.getLastReadBooks()
        lastReadBooks = lastReadBooks.unique()
        Integer siteId = getSiteId(request)
        if(redisService.("latestNotificationId_"+siteId)==null) dataProviderService.getLatestNotifications(siteId)
        def json = [lastReadBooks: lastReadBooks, latestNotificationId: redisService.("latestNotificationId_"+siteId)]
        render json as JSON
    }

    @Secured(['ROLE_USER'])
    def getRelatedBooks(){
        Integer siteId = getSiteId(request)
        String siteIdList = "1";
        if (siteId.intValue() != 1) {
            if (redisService.("siteIdList_" + siteId) == null) {
                SiteMst siteMst = SiteMst.findById(siteId)
                dataProviderService.getSiteIdList(siteId)
            }
            siteIdList = redisService.("siteIdList_" + siteId)
        }
        List lastReadBooks  = userManagementService.getLastReadBooks()
        if(lastReadBooks.size()>0) {
            def book = lastReadBooks[0];

            BooksTagDtl booksTag = BooksTagDtl.findByBookId(book.bookId)
            def keyName = booksTag.level + "_" + booksTag.syllabus.replaceAll("\\s+", "") + "_" + booksTag.grade.replaceAll("\\s+", "")
            if (redisService.("booksList_" + getSiteId(request) + "_" + keyName.replaceAll("\\s+", "")) == null) {
                dataProviderService.getBooksList(siteIdList, siteId, booksTag.level, booksTag.syllabus, booksTag.grade, "null", keyName.replaceAll("\\s+", ""))
            }
            def books = redisService.("booksList_" + getSiteId(request) + "_" + keyName.replaceAll("\\s+", ""))
            def json = ["books": books]
            render json as JSON
        }

        //else add the logic to get the tag from user preferences. To be implemented later

    }

    def quiz() {
        if ("bookTest".equals(params.mode)) {
            if (params.bookId != null) {
                BooksMst booksMst = dataProviderService.getBooksMst(new Integer(params.bookId))
                if (booksMst != null && "true".equals(booksMst.hasQuiz)) {
                    if (redisService.("commaSeperatedChapterIds_" + params.bookId) == null) dataProviderService.getCommaSeperatedChaptersList(params.bookId)
                    ["showHeader": "true", "name": booksMst.title, chaptersList: redisService.("commaSeperatedChapterIds_" + params.bookId),"title":"Online MCQ Tests"]
                }
            } else if (params.chaptersList != null) {
                ["showHeader": "true", "name": "Created Test", chaptersList: params.chaptersList]
            }
            else {
                redirect([uri: '/books/index'])
            }

        }else if(params.resId==null && params.mode=='weekly' || params.mode=='monthly'){
            ['title':'Online MCQ Tests']
        }
        else if(params.resId==null && !params.dailyTest){
            render "Quiz does not exist"
        }else if(params.resId==null && params.dailyTest){
            ['title':'Online MCQ Tests']
        }else{
            ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(params.resId))
            ["showHeader":"true","name":resourceDtl.resourceName]
        }
       
    }

    @Transactional
    def getHtmlsFileNm() {
        long bookId = 0
        Integer siteId = getSiteId(request)
        if(params.bookId != null && params.bookId != "") bookId = Long.parseLong(params.bookId)
        ResourceDtl resourceDtl

        if(params.chapterId!=null) resourceDtl = ResourceDtl.findByChapterId(new Long(params.chapterId))
            else resourceDtl = dataProviderService.getResourceDtl(new Long(params.resId))
        Long resId
        String zoomLevel
        String resourceName
        boolean isEpub = false,isSingleEpubFile = false
        String ebupChapterLink = "", notesType = "notes"
        resId = resourceDtl.parentId!=null?resourceDtl.parentId:resourceDtl.id
        zoomLevel = resourceDtl.zoomLevel
        resourceName = resourceDtl.resourceName
        def resourceDtlSub = ResourceDtlSub.findByResourceId(resId)

        def fileNm = ""
        if(resourceDtlSub!=null) fileNm = resourceDtlSub.filename
        if(resourceDtl.resLink.endsWith(".epub")){
            def file = new File(resourceDtl.resLink)
            if(file.exists()) {
                isEpub = true
                notesType ="epub"
                ebupChapterLink = resourceDtl.ebupChapterLink
                if(ebupChapterLink != null && !ebupChapterLink.isEmpty()) isSingleEpubFile = true
            }
        }else if(resourceDtl.filename.endsWith(".pdf")){
            notesType ="pdf"
        }else if(resourceDtl.filename.endsWith(".xhtml")||
                resourceDtl.filename.endsWith(".html") ||
                resourceDtl.filename.contains(".xhtml")||
                resourceDtl.filename.contains(".html")
        ){
            ebupChapterLink = resourceDtl.ebupChapterLink
        }
        if(bookId > 0){
            BooksMst booksMst = BooksMst.findById(bookId)
            com.wonderslate.publish.BooksPermission booksPermission = null
            User user = null
            if(springSecurityService.currentUser != null) user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
            String userName = ""
            if(booksMst != null && booksMst.price != null && booksMst.price > 0 && siteId!=80){
                boolean checkForPreviewChapter = true;
                if(user != null) {
                    userName = user.username
                    booksPermission = com.wonderslate.publish.BooksPermission.findByBookIdAndUsername(bookId,userName)
                    if (user.authorities.any {
                        it.authority == "ROLE_WS_CONTENT_CREATOR"
                    }) {
                        checkForPreviewChapter = false
                    }
                    if(booksPermission != null && (booksMst.createdBy.equals(userName) || booksPermission.poType.equals("PURCHASE")|| booksPermission.poType.equals("ADDEDFORFREE") || booksPermission.poType.equals("ADDEDFROMINSTITUTE"))) checkForPreviewChapter = false
                }
                if(checkForPreviewChapter){
                    ChaptersMst chaptersMst = ChaptersMst.findByBookIdAndPreviewChapter(bookId,"true")
                    if(chaptersMst != null) {
                        List<ResourceDtl> resourceDtls = ResourceDtl.findAllByChapterId(chaptersMst.id)
                        resourceDtls.each { res ->
                            if(res.ebupChapterLink != null && ! res.ebupChapterLink.isEmpty()){
                                def json = [fileNm: fileNm,isEpub: isEpub,isSingleEpubFile:isSingleEpubFile,ebupChapterLink:res.ebupChapterLink,previewChapterLink:res.ebupChapterLink,'zoomLevel':zoomLevel,resId:res.id,chapterId:res.chapterId]
                                render json as JSON
                            }
                        }
                    }
                }
            }
        }
        def json = [fileNm: fileNm,isEpub: isEpub,isSingleEpubFile:isSingleEpubFile,ebupChapterLink:ebupChapterLink,'notesType':notesType,'zoomLevel':zoomLevel,'resourceName':resourceName,resId:resourceDtl.id,chapterId:resourceDtl.chapterId]
        render json as JSON
    }

    @Transactional
    def getHtmlsFile() {

        try {
            ResourceDtl documentInstance = dataProviderService.getResourceDtl(new Long(params.resId))
            if(documentInstance != null && (documentInstance.quizMode!=null||canSeeResource(documentInstance))) {
                def filename = documentInstance.resLink.substring(0,
                        (documentInstance.filename.toLowerCase().endsWith(".pdf") || documentInstance.filename.toLowerCase().endsWith(".zip") ||
                                documentInstance.filename.toLowerCase().endsWith(".epub")?documentInstance.resLink.lastIndexOf("."):documentInstance.resLink.length()))+".ws"
                if (filename.endsWith(".xhtml.ws") ||
                        filename.endsWith(".html.ws") ||
                        filename.contains(".html") && filename.endsWith('.ws')||
                        filename.contains(".xhtml") && filename.endsWith('.ws')
                ) {
                    filename = filename.replaceAll(".ws", "")
                    if(filename.contains(".html#")||filename.contains(".xhtml#")){
                        filename = filename.split("#")[0]
                    }
                } else {
                    println "String does not end with .xhtml"
                }
                def file =  new File(filename)
                boolean fileExists = false
                if(file.exists()) fileExists = true
                else{
                    //for the copied resource case
                    filename = documentInstance.resLink.substring(0,documentInstance.resLink.lastIndexOf('/'))+"/"+documentInstance.filename+".ws"
                    file  = new File(filename)
                    if(file.exists()) fileExists = true
                }
                if(fileExists) {
                    //def startTime = new Date()
                    response.setContentType("text/html;charset=UTF-8")
                    response.setHeader("Content-Disposition", "inline;Filename=\"${file.getName().replaceAll(".ws",".html")}\"")
                    response.setHeader("Content-Length", "${file.length()}")

                    def fileInputStream = new FileInputStream(file)
                    def outputStream = response.getOutputStream()
                    byte[] buffer = new byte[4096]
                    int len
                    while ((len = fileInputStream.read(buffer)) > 0) {
                        outputStream.write(buffer, 0, len)
                    }

                    outputStream.flush()
                    outputStream.close()
                    fileInputStream.close()
                    //def endTime = new Date()
                    //println("time it takes is "+endTime.getTime()-startTime.getTime())
                } else {
                    //   userManagementService.sendMmail("<EMAIL>",null,null,
                    //             "HTML File not found","Reference resId is "+params.resId+" on server "+request.getServerName())
                }
            }else{
                def canSeeVal = canSeeResource(documentInstance)
                String debugStr = "canSeeResource returned "+canSeeVal+" for resId "+params.resId+" and username "+(springSecurityService.currentUser!=null?springSecurityService.currentUser.username:"null")
                println(debugStr)
            }
        }catch (Exception e){
            println("Exception in getHtmlsFile "+e.toString())
        }

    }



    @Transactional
    def downloadReadZippedFile() {
        Boolean isEpub = false
        Boolean isSplit = false
        def epubFiles = []
        ResourceDtl documentInstance = dataProviderService.getResourceDtl(new Long(params.resId))
        if(documentInstance != null && (documentInstance.quizMode!=null||canSeeResource(documentInstance))){
            def filename = ""

            if(documentInstance.filename.endsWith(".epub")){
                def file = new File(documentInstance.resLink)
                if(file.exists()) {
                    isEpub = true
//                    ebupChapterLink = resourceDtl.ebupChapterLink
//                    if(ebupChapterLink != null && !ebupChapterLink.isEmpty()) isSingleEpubFile = true
                }
            }else if(documentInstance.filename.endsWith(".xhtml") || documentInstance.filename.endsWith(".html")){
                def filePath = documentInstance.resLink.substring(0, documentInstance.resLink.lastIndexOf('/') + 1)
                def directory = new File(filePath)

                if (directory.exists() && directory.isDirectory()) {
                    directory.eachFileRecurse { file ->
                        if (file.isFile() && file.name.endsWith('.epub')) {
                            println(file.path)
                            epubFiles << file.path
                        }
                    }
                }
                if(epubFiles.size()>0){
                    isEpub=true
                    isSplit=true
                }
            }

            if(isEpub && !isSplit){
                filename = documentInstance.resLink
            }else if(isEpub && isSplit){
                filename = epubFiles[0]
            } else filename = documentInstance.resLink.substring(0,
                    (documentInstance.resLink.lastIndexOf(".")==-1?documentInstance.resLink.length():documentInstance.resLink.lastIndexOf(".")))+"_zippied.zip"

            def zippiedFile = new File(filename)

            if(!zippiedFile.exists() && !isEpub) {
                zipReadFiles(params.resId)

            }else{

            }

            zippiedFile = new File(filename)

            if(zippiedFile.exists()) {
                response.setContentType("APPLICATION/OCTET-STREAM")
                response.setHeader("Content-Disposition", "inline;Filename=\"${zippiedFile.getName()}\"")
                response.setHeader("Content-Length", "${zippiedFile.length()}")

                def fileInputStream = new FileInputStream(zippiedFile)
                def outputStream = response.getOutputStream()
                byte[] buffer = new byte[4096]
                int len
                while ((len = fileInputStream.read(buffer)) > 0) {
                    outputStream.write(buffer, 0, len)
                }

                outputStream.flush()
                outputStream.close()
                fileInputStream.close()
            }
        }
    }

    def zipReadFiles (String resId) {
        ResourceDtl documentInstance = dataProviderService.getResourceDtl(new Long(resId))

        if(documentInstance != null && (documentInstance.quizMode!=null||canSeeResource(documentInstance))){
            def zipFileNm
            def zipFile

            if((documentInstance.filename.toLowerCase().endsWith(".pdf") || documentInstance.filename.toLowerCase().endsWith(".zip") ||
                    documentInstance.filename.toLowerCase().endsWith(".epub")) && documentInstance.modifiedFile!=null && documentInstance.modifiedFile.trim()!="") {
                zipFileNm = documentInstance.filename

                if(zipFileNm.toLowerCase().endsWith(".pdf")) {
                    zipFileNm = zipFileNm.substring(0,zipFileNm.lastIndexOf("."))+".zip"
                }

                zipFile = new File(grailsApplication.config.grails.basedir.path+documentInstance.resLink.replace(zipFileNm, documentInstance.modifiedFile));
            }

            def wsFileNm = documentInstance.resLink.substring(0,
                    (documentInstance.resLink.lastIndexOf(".")==-1?documentInstance.resLink.length():documentInstance.resLink.lastIndexOf(".")))+".ws"

            def file = new File(wsFileNm)
            boolean fileExists = false
            if(file.exists()) fileExists = true
            else{
                //for the copied resource case
                wsFileNm = documentInstance.resLink.substring(0,documentInstance.resLink.lastIndexOf('/'))+"/"+documentInstance.filename+".ws"
                file  = new File(wsFileNm)
                if(file.exists()) fileExists = true
            }

            if(fileExists) {
                def zipFileName = documentInstance.resLink.substring(0,
                        (documentInstance.resLink.lastIndexOf(".")==-1?documentInstance.resLink.length():documentInstance.resLink.lastIndexOf(".")))+"_zippied.zip"

                FileOutputStream fos = new FileOutputStream(zipFileName);
                ZipOutputStream zos = new ZipOutputStream(fos);

                //adding ws file
                ZipEntry ze = new ZipEntry(wsFileNm.substring(wsFileNm.lastIndexOf("/")+1).replace(".ws",".html"));
                zos.putNextEntry(ze);

                //read the file and write to ZipOutputStream
                FileInputStream fis = new FileInputStream(file);
                byte[] buffer = new byte[1024];
                int len;
                while ((len = fis.read(buffer)) > 0) {
                    zos.write(buffer, 0, len);
                }

                zos.closeEntry();
                fis.close();

                if(zipFile!=null && zipFile.exists()) {
                    //adding zip file                    
                    ze = new ZipEntry(zipFileNm);
                    zos.putNextEntry(ze);

                    //read the file and write to ZipOutputStream
                    fis = new FileInputStream(zipFile);
                    buffer = new byte[1024];
                    while ((len = fis.read(buffer)) > 0) {
                        zos.write(buffer, 0, len);
                    }

                    zos.closeEntry();
                    fis.close();
                }

                zos.close();
                fos.close();
            }
        }
    }

    def oldLogin(){

    }

    def mobileLogin(){

    }

    def getAllChapterDetails(){
        if(redisService.("allChapterDetails_"+params.bookId)==null) {
            dataProviderService.getAllChapterDetails(new Long(params.bookId))
        }
        def json = [allChapters : redisService.("allChapterDetails_"+params.bookId)]
        render json as JSON

    }


    def getEncodedPdf(){
        byte[] input_file = new byte[0];
        String encodedString = "";
        String shortMessage = "";
        int requestNo = 0;
        ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(params.id))

        if(canSeeResource(resourceDtl)) {
            if (params.requestNo != null) requestNo = Integer.parseInt(params.requestNo);
            try {
                input_file = Files.readAllBytes(Paths.get(grailsApplication.config.grails.basedir.path + params.link));

                byte[] encodedBytes = Base64.getEncoder().encode(input_file);
                encodedString = new String(encodedBytes);
                int startingIndex = 0, lastIndex = 0;
                if (requestNo == 1) {
                    lastIndex = (encodedString.length() / 10) * (requestNo);
                    shortMessage = encodedString.substring(0, lastIndex)
                };
                else if (requestNo > 1 && requestNo < 10) {
                    startingIndex = (encodedString.length() / 10) * (requestNo - 1)
                    lastIndex = (encodedString.length() / 10) * (requestNo)
                    shortMessage = encodedString.substring(startingIndex, lastIndex)
                };
                else if (requestNo == 10) {
                    startingIndex = (encodedString.length() / 10) * (requestNo - 1)
                    lastIndex = ((encodedString.length() / 10) * (requestNo))
                    shortMessage = encodedString.substring(startingIndex, lastIndex)
                };
            } catch (Exception ex) {

            }
            if (requestNo > 0) {
                def json = ['shortMessage': shortMessage, 'requestNo': requestNo, 'id': params.id]
                render json as JSON
            } else if (requestNo == 0) {
                def json = ['encodedString': encodedString, 'id': params.id]
                render json as JSON
            }
        }

    }

    def sendInquiryEmail(){
        def name=params.name;
        def emailId=params.emailId;
        try {
            userManagementService.sendInquiryEmail("<EMAIL>", name, emailId)
            def json = ['success': "OK"]
            render json as JSON
        } catch (Exception e) {
            println "sending email failed " + e.toString()
        }
    }

    @Transactional
    def getPdfFile() {
        ResourceDtl documentInstance = dataProviderService.getResourceDtl(new Long(params.resId))
        if (canSeeResource(documentInstance)) {
            filenameCheck(documentInstance)
            try {
                String cdnLink = contentDeliveryService.generateSignedURL(grailsApplication.config.grails.cdn.pdf.path + documentInstance.resLink)
                response.setStatus(HttpServletResponse.SC_MOVED_TEMPORARILY)
                response.setHeader("Location", cdnLink)
            } catch (Exception ex) {
                println("Exception in getPdfFile " + ex.toString())
                render ""
            }
        } else {
            response.sendError(HttpServletResponse.SC_NOT_FOUND)
        }
    }

    @Transactional
    def filenameCheck(ResourceDtl documentInstance){
        String rightFileName = ""+documentInstance.id+".pdf"
        if(documentInstance.resLink.indexOf(rightFileName)==-1){
            //copy the file to the right file name
            Path source = Paths.get(grailsApplication.config.grails.basedir.path + documentInstance.resLink)
            Path destination = Paths.get(grailsApplication.config.grails.basedir.path + documentInstance.resLink.substring(0,documentInstance.resLink.lastIndexOf("/")+1)+rightFileName)
            Files.copy(source, destination, StandardCopyOption.REPLACE_EXISTING);

            //delete old file
            File file = new File(grailsApplication.config.grails.basedir.path + documentInstance.resLink)
            file.delete()
            //update filename and resLink in the database
            documentInstance.filename = rightFileName
            documentInstance.resLink = documentInstance.resLink.substring(0,documentInstance.resLink.lastIndexOf("/")+1)+rightFileName
            documentInstance.save(flush: true, failOnError: true)

        }
    }
    @Transactional
    def getPdfFileMobile(){
        ResourceDtl documentInstance = dataProviderService.getResourceDtl(new Long(params.resId))
        if (canSeeResource(documentInstance)) {
            filenameCheck(documentInstance)
            try {
                String cdnLink = contentDeliveryService.generateSignedURL(grailsApplication.config.grails.cdn.pdf.path + documentInstance.resLink)
                response.setStatus(HttpServletResponse.SC_MOVED_TEMPORARILY)
                response.setHeader("Location", cdnLink)
            } catch (Exception ex) {
                println("Exception in getPdfFile " + ex.toString())
                render ""
            }
        } else {
            response.sendError(HttpServletResponse.SC_NOT_FOUND)
        }
    }

    @Transactional
    def getEpubFile(){
        ResourceDtl documentInstance = dataProviderService.getResourceDtl(new Long(params.resId))
        if(canSeeResource(documentInstance)){
            try {
                String uri = request.getHeader("referer")
                def file = new File(documentInstance.resLink)
                String workingKey = "9E74748742AAB8342432FCF15E225793"
                String encResp= params.encryptedKey
                String sessionEncKey = session.getAttribute('epubEncKey')
                if(sessionEncKey != null && !sessionEncKey.isEmpty()) encResp = sessionEncKey
                AesCryptUtil aesUtil=new AesCryptUtil(workingKey)
                String decryptStr = aesUtil.decrypt(encResp)
                String originalStr = session.getAttribute('epubKey')
                String serverURL = request.getScheme()+"://"+request.getServerName()+
                        ("http".equals(request.getScheme()) && request.getServerPort() == 80 ||
                                "https".equals(request.getScheme()) && request.getServerPort() == 443 ? "" : ":" +
                                request.getServerPort())
                if (!uri.contains(serverURL+"/funlearn/") && file.exists() && !encResp.isEmpty() && encResp !=null && decryptStr.equals(originalStr)) { //
                    String newEncResp= "epub" + (int)(Math.random()*((9999-0000)+1))+0000
                    session.setAttribute('epubKey',newEncResp)
                    AesCryptUtil aesEncUtil=new AesCryptUtil(workingKey)
                    String encryptStr = aesEncUtil.encrypt(newEncResp)
                    session.setAttribute('epubEncKey',encryptStr)
                    response.setContentType("APPLICATION/OCTET-STREAM")
                    response.setHeader("Content-Disposition", "Attachment;Filename=demo.epub")
                    def fileInputStream = new FileInputStream(file)
                    def outputStream = response.getOutputStream()
                    byte[] buffer = new byte[4096];
                    int len;
                    while ((len = fileInputStream.read(buffer)) > 0) {
                        outputStream.write(buffer, 0, len);
                    }

                    outputStream.flush()
                    outputStream.close()
                    fileInputStream.close()
                } else render "";
            }
            catch (Exception e)
            {
                println("Exception in getEpubFile "+e.toString())
                render "";

            }
        }else{
            render ""
        }

    }

    def getVideoLink(){
        int siteId = getSiteId(request).intValue()
        ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(params.resId))
        if(canSeeResource(resourceDtl)) {
            def json = ["securityKey": securityService.encrypt(resourceDtl.resLink,siteId), "linkSrc":"/videos/src/"+(int)(Math.random()*((9999-0000)+1))+"/secureCheck?"+(Math.random()*((99999-0000)+1))]
            render json as JSON
        }else{
            def json = ["linkSrc": "Not authroised"]
            render json as JSON
        }
    }

    @Transactional
    def futureLearningProgram(){
        if (params?.registerUser) {
            def json
            String name = params?.name
            String emailId = params?.email
            String mobile = params?.mobile
            String address = params?.address
            String classStudying = params?.classStudying
            String school = params?.school
            FlpContactForm flpUserDetails = new FlpContactForm();
            try{
                flpUserDetails.name = name;
                flpUserDetails.email = emailId;
                flpUserDetails.mobile = mobile;
                flpUserDetails.school = school;
                flpUserDetails.address = address;
                flpUserDetails.classStudying = classStudying;
                flpUserDetails.save(failOnError: true, flush: true)
                json = ["status": "Success"]
            }catch (Exception ex){
                ex.printStackTrace();
                json = ["status":"Failed" , "error": "OOps!! Something went wrong!!"]
            }
            render json as JSON
        }
    }

    @Secured (['ROLE_WS_FLP'])
    @Transactional
    def flpReportDownload(){
        List data
        if(params?.downloadFlpReport){
            String sql = ""
            if(params?.poStartDate!=null || params.poEndDate!=null) {
                sql += "select fcf.name, fcf.email, fcf.mobile, fcf.school, fcf.class_studying, fcf.address, "+
                        " DATE_ADD(fcf.date_created, INTERVAL '5:30' HOUR_MINUTE) "+
                        " from flp_contact_form fcf "

                //using to check whether from-date or to-date values are available or not.
                boolean isAvailable = false;

                try {
                    if(params.poStartDate!=null && params.poStartDate!="" ){
                        isAvailable = true ;
                        sql += " where date(DATE_ADD(fcf.date_created, INTERVAL '5:30' HOUR_MINUTE)) >= STR_TO_DATE('" + params.poStartDate+"','%d-%m-%Y')"
                    }

                    if(params.poEndDate!=null && params.poEndDate!=""){
                        //logic for "where" & "and" which is based on whether StartDate provided or not.
                        if(isAvailable) sql+= " and " else sql+= " where " ;
                        sql += " date(DATE_ADD(fcf.date_created, INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('" + params.poEndDate+"','%d-%m-%Y') "
                    }

                    sql += " order by fcf.date_created desc"
                    def dataSource = grailsApplication.mainContext.getBean('dataSource_wscomm')
                    def sql1 = new Sql(dataSource)
                    def results = sql1.rows(sql)
                    def date
                    if(results!=null && results.size()>0) {
                        data = results.collect {report ->
                            date = report[6]
                            if (date != "") {
                                date = (new SimpleDateFormat("yyyy-MM-dd HH:mm")).format(date)
                            }
                            return [name:report[0], email:report[1],mobile:report[2], school:report[3]?:"", classStudying:report[4]?:"", address:report[5], dateCreated: report[6]]
                        }
                        while (data.remove(null)) {
                        }
                        List headers = [ "Name", "Email", "Mobile number", "School/College", "Class/Semester", "Address", "Date Created" ]

                        List withProperties = ["name", "email", "mobile", "school", "classStudying", "address", "dateCreated" ]

                        def fileName = "FLP_Registration_Data"+(params.poStartDate!=""?params.poStartDate+"_":"FromAny_")+
                                (params.poEndDate!=""?params.poEndDate+"_":"ToAny_")+(new Random()).nextInt(9999999)+".xlsx"

                        new WebXlsxExporter().with {
                            setResponseHeaders(response, fileName)
                            fillHeader(headers)
                            add(data, withProperties)
                            save(response.outputStream)
                        }
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        }
        [data:data, poStartDate:params?.poStartDate?:"", poEndDate:params?.poEndDate?:""]
    }
    def quizRead() {

        if ("bookTest".equals(params.mode)) {
            if (params.bookId != null) {
                BooksMst booksMst = dataProviderService.getBooksMst(new Integer(params.bookId))
                if (booksMst != null && "true".equals(booksMst.hasQuiz)) {
                    if (redisService.("commaSeperatedChapterIds_" + params.bookId) == null) dataProviderService.getCommaSeperatedChaptersList(params.bookId)
                    ["showHeader": "true", "name": booksMst.title, chaptersList: redisService.("commaSeperatedChapterIds_" + params.bookId)]
                }
            } else if (params.chaptersList != null) {
                ["showHeader": "true", "name": "Created Test", chaptersList: params.chaptersList]
            }
            else {

                redirect([uri: '/books/index'])
            }

        }
        else if(params.resId==null){
            render "Quiz does not exist"
        }else{
            ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(params.resId))
            ["showHeader":"true","name":resourceDtl.resourceName]
        }

    }

    def deleteMetainfoDirBySiteId(){
        List books =BooksMst.findAllBySiteId(new Integer(params.siteId))
        books.each {
            File extractDir = new File("upload/books/" + it.id + "/metadata")
            if (extractDir.exists()) deleteDirectory(extractDir.getAbsolutePath())
        }
    }

    def deleteDirectory(String dirStr) {
        File dir = new File(dirStr)
        if (dir.isDirectory()) {
            File[] children = dir.listFiles()
            for (int i = 0 ;i < children.length; i++) {
                boolean success = deleteDirectory(children[i].getAbsolutePath())
                if (!success) {
                    return false
                }
            }
        }
        // either file or an empty directory
        return dir.delete()
    }

    @Transactional @Secured(['ROLE_USER'])
    def downloadReadingMaterial(){
        String bookId=params.bookId
        String resId=params.resId
        String chapterId=params.chapterId
        ChaptersDownloadDtl chaptersDownloadDtl
        ResourceDtl documentInstance = dataProviderService.getResourceDtl(new Long(resId))
        if(canSeeResource(documentInstance)){
            try {
                Long siteId = session["siteId"]
                def instituteId = utilService.getInstituteId(request, siteId)
                chaptersDownloadDtl = ChaptersDownloadDtl.findByResIdAndInstituteIdAndUsernameAndSiteId(new Long(resId), new Long(instituteId), springSecurityService.currentUser.username, siteId)
                if (chaptersDownloadDtl == null) {
                    chaptersDownloadDtl = new ChaptersDownloadDtl(chapterId: new Long(chapterId), bookId: new Long(bookId), resId: new Long(resId), username: (springSecurityService.currentUser != null ? springSecurityService.currentUser.username : ""),
                            siteId: siteId, instituteId: instituteId)
                    chaptersDownloadDtl.save(flush: true, failOnError: true)
                    dataProviderService.getInstituteChapterDownloadCount(instituteId)
                    def downloadCount=redisService.("getInstituteChapterDownloadCount_"+instituteId)
                    if (downloadCount != null && Integer.parseInt(downloadCount) == 100) {
                        //send email here
                    try {
                        userManagementService.sendChapterDownloadError(
                                InstituteMst.findById(CourseBatchesDtl.findById(instituteId).id).name,
                                SiteMst.findById(getSiteId(request)).siteName,siteId
                        )
                    } catch (Exception e) {
                        println "sending library email failed " + e.toString()
                    }
                    }

                }
                if (documentInstance.filename.indexOf(".pdf") != -1) {
                    getPdfFile()
                } else if (documentInstance.filename.indexOf(".epub") != -1) {
                    getEpubFile()
                }
            }
            catch (Exception e)
            {
                println("Exception in downloadReadingMaterial "+e.toString())
                render "";

            }
        }
    }

    @Transactional @Secured(['ROLE_USER'])
    def checkDownloadAccess(params,request,siteId){
        boolean  insBookAccess=false
        boolean  bookDownload=false
        boolean  downloadFail=false
        def json
        String hasAccess="No"
        String exceededChapterLimit="false"
        String bookId=params.bookId
        String chapterId=params.chapterId
        String resId=params.resId
        def instituteId = utilService.getInstituteId(request,siteId)
        ResourceDtl documentInstance
        if(instituteId!=null) {
            if(redisService.("getInstituteChapterDownloadCount_"+instituteId)==null) {
                dataProviderService.getInstituteChapterDownloadCount(instituteId)
            }
            def downloadCount=redisService.("getInstituteChapterDownloadCount_"+instituteId)
            CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findByConductedByAndName(new Long(instituteId), "Default")
            BooksBatchDtl booksBatchDtl = BooksBatchDtl.findByBatchIdAndBookId(courseBatchesDtl.id,bookId)
            BooksMst booksMst = dataProviderService.getBooksMst(new Integer(bookId))
            if (instituteId != null && booksBatchDtl != null) insBookAccess = true
            List chaptersDownloadDtls = ChaptersDownloadDtl.findAllBySiteIdAndBookIdAndUsernameAndInstituteId(siteId, new Long(bookId), springSecurityService.currentUser.username, new Long(instituteId))
            ChaptersDownloadDtl chaptersDownloadDtl = ChaptersDownloadDtl.findByResIdAndInstituteIdAndUsernameAndSiteId(new Long(resId), new Long(instituteId), springSecurityService.currentUser.username, siteId)
            if(chaptersDownloadDtl==null  && Integer.parseInt(downloadCount)>=500) downloadFail=true
            if(!downloadFail) {
                if (chaptersDownloadDtl != null) {
                    bookDownload = true
                } else if (chaptersDownloadDtls.size() < booksMst.chapterDownloadCount) {
                    bookDownload = true
                }
                if ("Yes".equals(booksMst.downloadChapters) && insBookAccess && bookDownload) {
                    documentInstance = dataProviderService.getResourceDtl(new Long(resId))
                    hasAccess = "Yes"
                }
                if (hasAccess == "Yes" && chaptersDownloadDtls.size() > booksMst.chapterDownloadCount) {
                    exceededChapterLimit = "true"
                }
                json = ["hasAccess": hasAccess, chapterId: chapterId, bookId: bookId, resId: resId, exceededChapterLimit: exceededChapterLimit, fileName: documentInstance ? documentInstance.filename : ""]
            }else{
                json = ["download": "fail"]
            }
        }else{
            json = ["status": "no access"]
        }
        render json as JSON
    }

    @Transactional @Secured(['ROLE_USER'])
    def getUserAddedResourcesForBook(){
        def addedResources=[]
        if(springSecurityService.currentUser!=null) {
            List chaptersList = ChaptersMst.findAllByBookId(new Long(params.bookId))
            def chapterResourceDtl
            chaptersList.each {chapter->
                addedResources.add(metainfoService.getResourceDetails(new Long(chapter.id),springSecurityService.currentUser.username));
            }
        }
        def json = ['addedResources': addedResources]
        render json as JSON
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    def renderMCQContent(){
        ResourceDtl resourceDtl  = ResourceDtl.findById(new Long(params.resId))
        if (canSeeResource(resourceDtl)){
            ['canseeResource':true]
        }else{
            ['canseeResource':false]
        }
    }

    def deleteAccount(){
        Integer siteId = getSiteId(request)
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        ["title":"Delete User Account","siteName":siteMst.clientName]
    }
}
